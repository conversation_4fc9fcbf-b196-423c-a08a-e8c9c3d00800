{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\components\\\\LoadingScreen.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport '../styles/LoadingScreen.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingScreen = () => {\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"loading-screen\",\n    initial: {\n      opacity: 1\n    },\n    exit: {\n      opacity: 0\n    },\n    transition: {\n      duration: 0.5\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-content\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"loading-logo\",\n        initial: {\n          scale: 0.8,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        transition: {\n          duration: 0.8,\n          ease: \"easeOut\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-circle\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"Prof\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.h2, {\n        className: \"loading-title\",\n        initial: {\n          y: 20,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.3\n        },\n        children: \"Academic Excellence\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"loading-bar\",\n        initial: {\n          width: 0\n        },\n        animate: {\n          width: \"100%\"\n        },\n        transition: {\n          duration: 1.5,\n          delay: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-progress\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n        className: \"loading-text\",\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.8\n        },\n        children: \"Loading Portfolio...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingScreen;\nexport default LoadingScreen;\nvar _c;\n$RefreshReg$(_c, \"LoadingScreen\");", "map": {"version": 3, "names": ["React", "motion", "jsxDEV", "_jsxDEV", "LoadingScreen", "div", "className", "initial", "opacity", "exit", "transition", "duration", "children", "scale", "animate", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "h2", "y", "delay", "width", "p", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/components/LoadingScreen.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport '../styles/LoadingScreen.css';\n\nconst LoadingScreen = () => {\n  return (\n    <motion.div\n      className=\"loading-screen\"\n      initial={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <div className=\"loading-content\">\n        <motion.div\n          className=\"loading-logo\"\n          initial={{ scale: 0.8, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <div className=\"logo-circle\">\n            <span className=\"logo-text\">Prof</span>\n          </div>\n        </motion.div>\n        \n        <motion.h2\n          className=\"loading-title\"\n          initial={{ y: 20, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n        >\n          Academic Excellence\n        </motion.h2>\n        \n        <motion.div\n          className=\"loading-bar\"\n          initial={{ width: 0 }}\n          animate={{ width: \"100%\" }}\n          transition={{ duration: 1.5, delay: 0.5 }}\n        >\n          <div className=\"loading-progress\"></div>\n        </motion.div>\n        \n        <motion.p\n          className=\"loading-text\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n        >\n          Loading Portfolio...\n        </motion.p>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default LoadingScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA,CAACF,MAAM,CAACI,GAAG;IACTC,SAAS,EAAC,gBAAgB;IAC1BC,OAAO,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAE;IACxBC,IAAI,EAAE;MAAED,OAAO,EAAE;IAAE,CAAE;IACrBE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,eAE9BT,OAAA;MAAKG,SAAS,EAAC,iBAAiB;MAAAM,QAAA,gBAC9BT,OAAA,CAACF,MAAM,CAACI,GAAG;QACTC,SAAS,EAAC,cAAc;QACxBC,OAAO,EAAE;UAAEM,KAAK,EAAE,GAAG;UAAEL,OAAO,EAAE;QAAE,CAAE;QACpCM,OAAO,EAAE;UAAED,KAAK,EAAE,CAAC;UAAEL,OAAO,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEI,IAAI,EAAE;QAAU,CAAE;QAAAH,QAAA,eAE/CT,OAAA;UAAKG,SAAS,EAAC,aAAa;UAAAM,QAAA,eAC1BT,OAAA;YAAMG,SAAS,EAAC,WAAW;YAAAM,QAAA,EAAC;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEbhB,OAAA,CAACF,MAAM,CAACmB,EAAE;QACRd,SAAS,EAAC,eAAe;QACzBC,OAAO,EAAE;UAAEc,CAAC,EAAE,EAAE;UAAEb,OAAO,EAAE;QAAE,CAAE;QAC/BM,OAAO,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEb,OAAO,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEW,KAAK,EAAE;QAAI,CAAE;QAAAV,QAAA,EAC3C;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAEZhB,OAAA,CAACF,MAAM,CAACI,GAAG;QACTC,SAAS,EAAC,aAAa;QACvBC,OAAO,EAAE;UAAEgB,KAAK,EAAE;QAAE,CAAE;QACtBT,OAAO,EAAE;UAAES,KAAK,EAAE;QAAO,CAAE;QAC3Bb,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEW,KAAK,EAAE;QAAI,CAAE;QAAAV,QAAA,eAE1CT,OAAA;UAAKG,SAAS,EAAC;QAAkB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAEbhB,OAAA,CAACF,MAAM,CAACuB,CAAC;QACPlB,SAAS,EAAC,cAAc;QACxBC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBM,OAAO,EAAE;UAAEN,OAAO,EAAE;QAAE,CAAE;QACxBE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEW,KAAK,EAAE;QAAI,CAAE;QAAAV,QAAA,EAC3C;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACM,EAAA,GAjDIrB,aAAa;AAmDnB,eAAeA,aAAa;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}