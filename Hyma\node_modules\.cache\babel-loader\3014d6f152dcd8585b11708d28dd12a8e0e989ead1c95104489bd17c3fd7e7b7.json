{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\pages\\\\Home.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaDownload, FaEnvelope, FaLinkedin, FaGoogle, FaGraduationCap, FaArrowRight } from 'react-icons/fa';\nimport '../styles/Home.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  const navigationCards = [{\n    title: \"About Me\",\n    description: \"Learn about my background, expertise, and academic journey\",\n    icon: \"👨‍🏫\",\n    link: \"/about\",\n    color: \"from-blue-500 to-purple-600\"\n  }, {\n    title: \"Research\",\n    description: \"Explore my research areas, current projects, and innovations\",\n    icon: \"🔬\",\n    link: \"/research\",\n    color: \"from-green-500 to-teal-600\"\n  }, {\n    title: \"Publications\",\n    description: \"Browse my academic publications and research papers\",\n    icon: \"📚\",\n    link: \"/publications\",\n    color: \"from-orange-500 to-red-600\"\n  }, {\n    title: \"Teaching\",\n    description: \"Discover my courses, teaching philosophy, and achievements\",\n    icon: \"🎓\",\n    link: \"/teaching\",\n    color: \"from-purple-500 to-pink-600\"\n  }, {\n    title: \"Contact\",\n    description: \"Get in touch for collaborations and opportunities\",\n    icon: \"📧\",\n    link: \"/contact\",\n    color: \"from-indigo-500 to-blue-600\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-background\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-particles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-content\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"hero-text\",\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n              className: \"hero-title\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.4\n              },\n              children: [\"Ms. \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"gradient-text\",\n                children: \"Hymavathi Thottathyl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              className: \"hero-subtitle\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.6\n              },\n              children: \"Assistant Professor & PhD Scholar in Computer Science\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              className: \"hero-description\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.8\n              },\n              children: \"Specializing in artificial intelligence, machine learning, and data science with focus on breast cancer gene prediction and network security. Passionate about research and education.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"hero-buttons\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 1.0\n              },\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contact\",\n                className: \"btn btn-primary\",\n                children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this), \"Get In Touch\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-outline\",\n                children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this), \"Download CV\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"hero-social\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 1.2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                \"aria-label\": \"LinkedIn\",\n                children: /*#__PURE__*/_jsxDEV(FaLinkedin, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                \"aria-label\": \"Google Scholar\",\n                children: /*#__PURE__*/_jsxDEV(FaGoogle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                \"aria-label\": \"Academic Profile\",\n                children: /*#__PURE__*/_jsxDEV(FaGraduationCap, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"hero-image\",\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 1,\n              delay: 0.5\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-image\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/images/hymavathi.jpg\",\n                  alt: \"Ms. Hymavathi Thottathyl\",\n                  className: \"profile-photo\",\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.parentElement.innerHTML = `\n                        <div style=\"width: 100%; height: 100%; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem;\">\n                          👩‍🏫\n                        </div>\n                      `;\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-decoration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"navigation-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n          className: \"section-title\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          children: \"Explore My Academic Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navigation-grid\",\n          children: navigationCards.map((card, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"navigation-card\",\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            whileHover: {\n              y: -10,\n              scale: 1.02\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: card.link,\n              className: \"card-link\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `card-gradient bg-gradient-to-br ${card.color}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card-icon\",\n                    children: card.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"card-title\",\n                    children: card.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"card-description\",\n                    children: card.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card-arrow\",\n                    children: /*#__PURE__*/_jsxDEV(FaArrowRight, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)\n          }, card.title, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"stats-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats-grid\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"stat-item\",\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Publications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"stat-item\",\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Patent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"stat-item\",\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: \"10+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Certifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"stat-item\",\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.4\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: \"8+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Workshops\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "motion", "Link", "FaDownload", "FaEnvelope", "FaLinkedin", "FaGoogle", "FaGraduationCap", "FaArrowRight", "jsxDEV", "_jsxDEV", "Home", "navigationCards", "title", "description", "icon", "link", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "h1", "p", "to", "href", "scale", "src", "alt", "onError", "e", "target", "style", "display", "parentElement", "innerHTML", "h2", "whileInView", "viewport", "once", "map", "card", "index", "whileHover", "whileTap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/pages/Home.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaDownload, FaEnvelope, FaLinkedin, FaGoogle, FaGraduationCap, FaArrowRight } from 'react-icons/fa';\nimport '../styles/Home.css';\n\nconst Home = () => {\n  const navigationCards = [\n    {\n      title: \"About Me\",\n      description: \"Learn about my background, expertise, and academic journey\",\n      icon: \"👨‍🏫\",\n      link: \"/about\",\n      color: \"from-blue-500 to-purple-600\"\n    },\n    {\n      title: \"Research\",\n      description: \"Explore my research areas, current projects, and innovations\",\n      icon: \"🔬\",\n      link: \"/research\",\n      color: \"from-green-500 to-teal-600\"\n    },\n    {\n      title: \"Publications\",\n      description: \"Browse my academic publications and research papers\",\n      icon: \"📚\",\n      link: \"/publications\",\n      color: \"from-orange-500 to-red-600\"\n    },\n    {\n      title: \"Teaching\",\n      description: \"Discover my courses, teaching philosophy, and achievements\",\n      icon: \"🎓\",\n      link: \"/teaching\",\n      color: \"from-purple-500 to-pink-600\"\n    },\n    {\n      title: \"Contact\",\n      description: \"Get in touch for collaborations and opportunities\",\n      icon: \"📧\",\n      link: \"/contact\",\n      color: \"from-indigo-500 to-blue-600\"\n    }\n  ];\n\n  return (\n    <div className=\"home-page\">\n      {/* Hero Section */}\n      <section className=\"hero-section\">\n        <div className=\"hero-background\">\n          <div className=\"hero-particles\"></div>\n        </div>\n        \n        <div className=\"container\">\n          <div className=\"hero-content\">\n            <motion.div\n              className=\"hero-text\"\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              <motion.h1\n                className=\"hero-title\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.4 }}\n              >\n                Ms. <span className=\"gradient-text\">Hymavathi Thottathyl</span>\n              </motion.h1>\n              \n              <motion.p\n                className=\"hero-subtitle\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.6 }}\n              >\n                Assistant Professor & PhD Scholar in Computer Science\n              </motion.p>\n              \n              <motion.p\n                className=\"hero-description\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.8 }}\n              >\n                Specializing in artificial intelligence, machine learning, and data science with focus on\n                breast cancer gene prediction and network security. Passionate about research and education.\n              </motion.p>\n              \n              <motion.div\n                className=\"hero-buttons\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 1.0 }}\n              >\n                <Link to=\"/contact\" className=\"btn btn-primary\">\n                  <FaEnvelope />\n                  Get In Touch\n                </Link>\n                <button className=\"btn btn-outline\">\n                  <FaDownload />\n                  Download CV\n                </button>\n              </motion.div>\n              \n              <motion.div\n                className=\"hero-social\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 1.2 }}\n              >\n                <a href=\"#\" className=\"social-link\" aria-label=\"LinkedIn\">\n                  <FaLinkedin />\n                </a>\n                <a href=\"#\" className=\"social-link\" aria-label=\"Google Scholar\">\n                  <FaGoogle />\n                </a>\n                <a href=\"#\" className=\"social-link\" aria-label=\"Academic Profile\">\n                  <FaGraduationCap />\n                </a>\n              </motion.div>\n            </motion.div>\n            \n            <motion.div\n              className=\"hero-image\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 1, delay: 0.5 }}\n            >\n              <div className=\"image-container\">\n                <div className=\"profile-image\">\n                  <img\n                    src=\"/images/hymavathi.jpg\"\n                    alt=\"Ms. Hymavathi Thottathyl\"\n                    className=\"profile-photo\"\n                    onError={(e) => {\n                      e.target.style.display = 'none';\n                      e.target.parentElement.innerHTML = `\n                        <div style=\"width: 100%; height: 100%; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem;\">\n                          👩‍🏫\n                        </div>\n                      `;\n                    }}\n                  />\n                </div>\n                <div className=\"image-decoration\"></div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Navigation Cards Section */}\n      <section className=\"navigation-section\">\n        <div className=\"container\">\n          <motion.h2\n            className=\"section-title\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n          >\n            Explore My Academic Portfolio\n          </motion.h2>\n          \n          <div className=\"navigation-grid\">\n            {navigationCards.map((card, index) => (\n              <motion.div\n                key={card.title}\n                className=\"navigation-card\"\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ y: -10, scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <Link to={card.link} className=\"card-link\">\n                  <div className={`card-gradient bg-gradient-to-br ${card.color}`}>\n                    <div className=\"card-content\">\n                      <div className=\"card-icon\">{card.icon}</div>\n                      <h3 className=\"card-title\">{card.title}</h3>\n                      <p className=\"card-description\">{card.description}</p>\n                      <div className=\"card-arrow\">\n                        <FaArrowRight />\n                      </div>\n                    </div>\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Quick Stats Section */}\n      <section className=\"stats-section\">\n        <div className=\"container\">\n          <div className=\"stats-grid\">\n            <motion.div\n              className=\"stat-item\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <div className=\"stat-number\">4</div>\n              <div className=\"stat-label\">Publications</div>\n            </motion.div>\n            <motion.div\n              className=\"stat-item\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <div className=\"stat-number\">1</div>\n              <div className=\"stat-label\">Patent</div>\n            </motion.div>\n            <motion.div\n              className=\"stat-item\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <div className=\"stat-number\">10+</div>\n              <div className=\"stat-label\">Certifications</div>\n            </motion.div>\n            <motion.div\n              className=\"stat-item\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              viewport={{ once: true }}\n            >\n              <div className=\"stat-number\">8+</div>\n              <div className=\"stat-label\">Workshops</div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,YAAY,QAAQ,gBAAgB;AAC5G,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,MAAMC,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,8DAA8D;IAC3EC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,qDAAqD;IAClEC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,mDAAmD;IAChEC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBT,OAAA;MAASQ,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC/BT,OAAA;QAAKQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BT,OAAA;UAAKQ,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eAENb,OAAA;QAAKQ,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBT,OAAA;UAAKQ,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BT,OAAA,CAACT,MAAM,CAACuB,GAAG;YACTN,SAAS,EAAC,WAAW;YACrBO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAAAZ,QAAA,gBAE1CT,OAAA,CAACT,MAAM,CAAC+B,EAAE;cACRd,SAAS,EAAC,YAAY;cACtBO,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAZ,QAAA,GAC3C,MACK,eAAAT,OAAA;gBAAMQ,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAEZb,OAAA,CAACT,MAAM,CAACgC,CAAC;cACPf,SAAS,EAAC,eAAe;cACzBO,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAZ,QAAA,EAC3C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAEXb,OAAA,CAACT,MAAM,CAACgC,CAAC;cACPf,SAAS,EAAC,kBAAkB;cAC5BO,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAZ,QAAA,EAC3C;YAGD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAEXb,OAAA,CAACT,MAAM,CAACuB,GAAG;cACTN,SAAS,EAAC,cAAc;cACxBO,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAZ,QAAA,gBAE1CT,OAAA,CAACR,IAAI;gBAACgC,EAAE,EAAC,UAAU;gBAAChB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC7CT,OAAA,CAACN,UAAU;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEhB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPb,OAAA;gBAAQQ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBACjCT,OAAA,CAACP,UAAU;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEhB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEbb,OAAA,CAACT,MAAM,CAACuB,GAAG;cACTN,SAAS,EAAC,aAAa;cACvBO,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAZ,QAAA,gBAE1CT,OAAA;gBAAGyB,IAAI,EAAC,GAAG;gBAACjB,SAAS,EAAC,aAAa;gBAAC,cAAW,UAAU;gBAAAC,QAAA,eACvDT,OAAA,CAACL,UAAU;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACJb,OAAA;gBAAGyB,IAAI,EAAC,GAAG;gBAACjB,SAAS,EAAC,aAAa;gBAAC,cAAW,gBAAgB;gBAAAC,QAAA,eAC7DT,OAAA,CAACJ,QAAQ;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACJb,OAAA;gBAAGyB,IAAI,EAAC,GAAG;gBAACjB,SAAS,EAAC,aAAa;gBAAC,cAAW,kBAAkB;gBAAAC,QAAA,eAC/DT,OAAA,CAACH,eAAe;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEbb,OAAA,CAACT,MAAM,CAACuB,GAAG;YACTN,SAAS,EAAC,YAAY;YACtBO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAI,CAAE;YACpCR,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAE,CAAE;YAClCP,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAI,CAAE;YAAAZ,QAAA,eAExCT,OAAA;cAAKQ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BT,OAAA;gBAAKQ,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BT,OAAA;kBACE2B,GAAG,EAAC,uBAAuB;kBAC3BC,GAAG,EAAC,0BAA0B;kBAC9BpB,SAAS,EAAC,eAAe;kBACzBqB,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;oBAC/BH,CAAC,CAACC,MAAM,CAACG,aAAa,CAACC,SAAS,GAAG;AACzD;AACA;AACA;AACA,uBAAuB;kBACH;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNb,OAAA;gBAAKQ,SAAS,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVb,OAAA;MAASQ,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACrCT,OAAA;QAAKQ,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBT,OAAA,CAACT,MAAM,CAAC6C,EAAE;UACR5B,SAAS,EAAC,eAAe;UACzBO,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BoB,WAAW,EAAE;YAAErB,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BkB,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAA9B,QAAA,EAC1B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZb,OAAA;UAAKQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BP,eAAe,CAACsC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/B1C,OAAA,CAACT,MAAM,CAACuB,GAAG;YAETN,SAAS,EAAC,iBAAiB;YAC3BO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BoB,WAAW,EAAE;cAAErB,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAEqB,KAAK,GAAG;YAAI,CAAE;YAClDJ,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBI,UAAU,EAAE;cAAE1B,CAAC,EAAE,CAAC,EAAE;cAAES,KAAK,EAAE;YAAK,CAAE;YACpCkB,QAAQ,EAAE;cAAElB,KAAK,EAAE;YAAK,CAAE;YAAAjB,QAAA,eAE1BT,OAAA,CAACR,IAAI;cAACgC,EAAE,EAAEiB,IAAI,CAACnC,IAAK;cAACE,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxCT,OAAA;gBAAKQ,SAAS,EAAE,mCAAmCiC,IAAI,CAAClC,KAAK,EAAG;gBAAAE,QAAA,eAC9DT,OAAA;kBAAKQ,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BT,OAAA;oBAAKQ,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEgC,IAAI,CAACpC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5Cb,OAAA;oBAAIQ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEgC,IAAI,CAACtC;kBAAK;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5Cb,OAAA;oBAAGQ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAEgC,IAAI,CAACrC;kBAAW;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtDb,OAAA;oBAAKQ,SAAS,EAAC,YAAY;oBAAAC,QAAA,eACzBT,OAAA,CAACF,YAAY;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GApBF4B,IAAI,CAACtC,KAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVb,OAAA;MAASQ,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChCT,OAAA;QAAKQ,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBT,OAAA;UAAKQ,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBT,OAAA,CAACT,MAAM,CAACuB,GAAG;YACTN,SAAS,EAAC,WAAW;YACrBO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAI,CAAE;YACpCW,WAAW,EAAE;cAAErB,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAE,CAAE;YACtCP,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CiB,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA9B,QAAA,gBAEzBT,OAAA;cAAKQ,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCb,OAAA;cAAKQ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACbb,OAAA,CAACT,MAAM,CAACuB,GAAG;YACTN,SAAS,EAAC,WAAW;YACrBO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAI,CAAE;YACpCW,WAAW,EAAE;cAAErB,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAE,CAAE;YACtCP,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CiB,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA9B,QAAA,gBAEzBT,OAAA;cAAKQ,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCb,OAAA;cAAKQ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACbb,OAAA,CAACT,MAAM,CAACuB,GAAG;YACTN,SAAS,EAAC,WAAW;YACrBO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAI,CAAE;YACpCW,WAAW,EAAE;cAAErB,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAE,CAAE;YACtCP,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CiB,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA9B,QAAA,gBAEzBT,OAAA;cAAKQ,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCb,OAAA;cAAKQ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACbb,OAAA,CAACT,MAAM,CAACuB,GAAG;YACTN,SAAS,EAAC,WAAW;YACrBO,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAI,CAAE;YACpCW,WAAW,EAAE;cAAErB,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAE,CAAE;YACtCP,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CiB,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA9B,QAAA,gBAEzBT,OAAA;cAAKQ,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCb,OAAA;cAAKQ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACgC,EAAA,GA9OI5C,IAAI;AAgPV,eAAeA,IAAI;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}