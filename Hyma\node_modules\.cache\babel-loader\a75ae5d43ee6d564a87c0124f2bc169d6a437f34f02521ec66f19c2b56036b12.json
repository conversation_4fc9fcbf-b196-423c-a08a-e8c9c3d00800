{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBars, FaTimes } from 'react-icons/fa';\nimport '../styles/Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  activeSection\n}) => {\n  _s();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const navItems = [{\n    id: 'home',\n    label: 'Home'\n  }, {\n    id: 'about',\n    label: 'About'\n  }, {\n    id: 'research',\n    label: 'Research'\n  }, {\n    id: 'publications',\n    label: 'Publications'\n  }, {\n    id: 'teaching',\n    label: 'Teaching'\n  }, {\n    id: 'contact',\n    label: 'Contact'\n  }];\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const scrollToSection = sectionId => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      const offsetTop = element.offsetTop - 80;\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n    setIsMobileMenuOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(motion.header, {\n    className: `header ${isScrolled ? 'scrolled' : ''}`,\n    initial: {\n      y: -100\n    },\n    animate: {\n      y: 0\n    },\n    transition: {\n      duration: 0.6,\n      ease: \"easeOut\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"logo\",\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-circle\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Prof\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"Dr. Academic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"nav-desktop\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"nav-list\",\n            children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `nav-link ${activeSection === item.id ? 'active' : ''}`,\n                onClick: () => scrollToSection(item.id),\n                children: [item.label, activeSection === item.id && /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"nav-indicator\",\n                  layoutId: \"nav-indicator\",\n                  transition: {\n                    type: \"spring\",\n                    stiffness: 300,\n                    damping: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 19\n              }, this)\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"mobile-menu-toggle\",\n          onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n          \"aria-label\": \"Toggle mobile menu\",\n          children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 33\n          }, this) : /*#__PURE__*/_jsxDEV(FaBars, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: `mobile-menu ${isMobileMenuOpen ? 'open' : ''}`,\n      initial: {\n        opacity: 0,\n        height: 0\n      },\n      animate: {\n        opacity: isMobileMenuOpen ? 1 : 0,\n        height: isMobileMenuOpen ? 'auto' : 0\n      },\n      transition: {\n        duration: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"mobile-nav\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"mobile-nav-list\",\n          children: navItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.li, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: isMobileMenuOpen ? 1 : 0,\n              x: isMobileMenuOpen ? 0 : -20\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `mobile-nav-link ${activeSection === item.id ? 'active' : ''}`,\n              onClick: () => scrollToSection(item.id),\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"0+zEKVBL95ILuBb5rHE6ViYOHu8=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "FaBars", "FaTimes", "jsxDEV", "_jsxDEV", "Header", "activeSection", "_s", "isScrolled", "setIsScrolled", "isMobileMenuOpen", "setIsMobileMenuOpen", "navItems", "id", "label", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "scrollToSection", "sectionId", "element", "document", "getElementById", "offsetTop", "scrollTo", "top", "behavior", "header", "className", "initial", "y", "animate", "transition", "duration", "ease", "children", "div", "whileHover", "scale", "whileTap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "onClick", "layoutId", "type", "stiffness", "damping", "opacity", "height", "index", "li", "x", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/components/Header.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBars, FaTimes } from 'react-icons/fa';\nimport '../styles/Header.css';\n\nconst Header = ({ activeSection }) => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const navItems = [\n    { id: 'home', label: 'Home' },\n    { id: 'about', label: 'About' },\n    { id: 'research', label: 'Research' },\n    { id: 'publications', label: 'Publications' },\n    { id: 'teaching', label: 'Teaching' },\n    { id: 'contact', label: 'Contact' }\n  ];\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToSection = (sectionId) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      const offsetTop = element.offsetTop - 80;\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n    setIsMobileMenuOpen(false);\n  };\n\n  return (\n    <motion.header\n      className={`header ${isScrolled ? 'scrolled' : ''}`}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: \"easeOut\" }}\n    >\n      <div className=\"container\">\n        <div className=\"header-content\">\n          <motion.div\n            className=\"logo\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <div className=\"logo-circle\">\n              <span>Prof</span>\n            </div>\n            <span className=\"logo-text\">Dr. Academic</span>\n          </motion.div>\n\n          <nav className=\"nav-desktop\">\n            <ul className=\"nav-list\">\n              {navItems.map((item) => (\n                <li key={item.id}>\n                  <button\n                    className={`nav-link ${activeSection === item.id ? 'active' : ''}`}\n                    onClick={() => scrollToSection(item.id)}\n                  >\n                    {item.label}\n                    {activeSection === item.id && (\n                      <motion.div\n                        className=\"nav-indicator\"\n                        layoutId=\"nav-indicator\"\n                        transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                      />\n                    )}\n                  </button>\n                </li>\n              ))}\n            </ul>\n          </nav>\n\n          <button\n            className=\"mobile-menu-toggle\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            aria-label=\"Toggle mobile menu\"\n          >\n            {isMobileMenuOpen ? <FaTimes /> : <FaBars />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <motion.div\n        className={`mobile-menu ${isMobileMenuOpen ? 'open' : ''}`}\n        initial={{ opacity: 0, height: 0 }}\n        animate={{\n          opacity: isMobileMenuOpen ? 1 : 0,\n          height: isMobileMenuOpen ? 'auto' : 0\n        }}\n        transition={{ duration: 0.3 }}\n      >\n        <nav className=\"mobile-nav\">\n          <ul className=\"mobile-nav-list\">\n            {navItems.map((item, index) => (\n              <motion.li\n                key={item.id}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{\n                  opacity: isMobileMenuOpen ? 1 : 0,\n                  x: isMobileMenuOpen ? 0 : -20\n                }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <button\n                  className={`mobile-nav-link ${activeSection === item.id ? 'active' : ''}`}\n                  onClick={() => scrollToSection(item.id)}\n                >\n                  {item.label}\n                </button>\n              </motion.li>\n            ))}\n          </ul>\n        </nav>\n      </motion.div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAChD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMc,QAAQ,GAAG,CACf;IAAEC,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC7B;IAAED,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC/B;IAAED,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACrC;IAAED,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC7C;IAAED,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACrC;IAAED,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,CACpC;EAEDf,SAAS,CAAC,MAAM;IACd,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzBN,aAAa,CAACO,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAIC,SAAS,IAAK;IACrC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC;IAClD,IAAIC,OAAO,EAAE;MACX,MAAMG,SAAS,GAAGH,OAAO,CAACG,SAAS,GAAG,EAAE;MACxCT,MAAM,CAACU,QAAQ,CAAC;QACdC,GAAG,EAAEF,SAAS;QACdG,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACAjB,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,oBACEP,OAAA,CAACJ,MAAM,CAAC6B,MAAM;IACZC,SAAS,EAAE,UAAUtB,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;IACpDuB,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,UAAU,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAU,CAAE;IAAAC,QAAA,gBAE/CjC,OAAA;MAAK0B,SAAS,EAAC,WAAW;MAAAO,QAAA,eACxBjC,OAAA;QAAK0B,SAAS,EAAC,gBAAgB;QAAAO,QAAA,gBAC7BjC,OAAA,CAACJ,MAAM,CAACsC,GAAG;UACTR,SAAS,EAAC,MAAM;UAChBS,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAH,QAAA,gBAE1BjC,OAAA;YAAK0B,SAAS,EAAC,aAAa;YAAAO,QAAA,eAC1BjC,OAAA;cAAAiC,QAAA,EAAM;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACNzC,OAAA;YAAM0B,SAAS,EAAC,WAAW;YAAAO,QAAA,EAAC;UAAY;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAEbzC,OAAA;UAAK0B,SAAS,EAAC,aAAa;UAAAO,QAAA,eAC1BjC,OAAA;YAAI0B,SAAS,EAAC,UAAU;YAAAO,QAAA,EACrBzB,QAAQ,CAACkC,GAAG,CAAEC,IAAI,iBACjB3C,OAAA;cAAAiC,QAAA,eACEjC,OAAA;gBACE0B,SAAS,EAAE,YAAYxB,aAAa,KAAKyC,IAAI,CAAClC,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACnEmC,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAAC2B,IAAI,CAAClC,EAAE,CAAE;gBAAAwB,QAAA,GAEvCU,IAAI,CAACjC,KAAK,EACVR,aAAa,KAAKyC,IAAI,CAAClC,EAAE,iBACxBT,OAAA,CAACJ,MAAM,CAACsC,GAAG;kBACTR,SAAS,EAAC,eAAe;kBACzBmB,QAAQ,EAAC,eAAe;kBACxBf,UAAU,EAAE;oBAAEgB,IAAI,EAAE,QAAQ;oBAAEC,SAAS,EAAE,GAAG;oBAAEC,OAAO,EAAE;kBAAG;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC,GAbFE,IAAI,CAAClC,EAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENzC,OAAA;UACE0B,SAAS,EAAC,oBAAoB;UAC9BkB,OAAO,EAAEA,CAAA,KAAMrC,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;UACtD,cAAW,oBAAoB;UAAA2B,QAAA,EAE9B3B,gBAAgB,gBAAGN,OAAA,CAACF,OAAO;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGzC,OAAA,CAACH,MAAM;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA,CAACJ,MAAM,CAACsC,GAAG;MACTR,SAAS,EAAE,eAAepB,gBAAgB,GAAG,MAAM,GAAG,EAAE,EAAG;MAC3DqB,OAAO,EAAE;QAAEsB,OAAO,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAE;MACnCrB,OAAO,EAAE;QACPoB,OAAO,EAAE3C,gBAAgB,GAAG,CAAC,GAAG,CAAC;QACjC4C,MAAM,EAAE5C,gBAAgB,GAAG,MAAM,GAAG;MACtC,CAAE;MACFwB,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAE,QAAA,eAE9BjC,OAAA;QAAK0B,SAAS,EAAC,YAAY;QAAAO,QAAA,eACzBjC,OAAA;UAAI0B,SAAS,EAAC,iBAAiB;UAAAO,QAAA,EAC5BzB,QAAQ,CAACkC,GAAG,CAAC,CAACC,IAAI,EAAEQ,KAAK,kBACxBnD,OAAA,CAACJ,MAAM,CAACwD,EAAE;YAERzB,OAAO,EAAE;cAAEsB,OAAO,EAAE,CAAC;cAAEI,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCxB,OAAO,EAAE;cACPoB,OAAO,EAAE3C,gBAAgB,GAAG,CAAC,GAAG,CAAC;cACjC+C,CAAC,EAAE/C,gBAAgB,GAAG,CAAC,GAAG,CAAC;YAC7B,CAAE;YACFwB,UAAU,EAAE;cAAEwB,KAAK,EAAEH,KAAK,GAAG;YAAI,CAAE;YAAAlB,QAAA,eAEnCjC,OAAA;cACE0B,SAAS,EAAE,mBAAmBxB,aAAa,KAAKyC,IAAI,CAAClC,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC1EmC,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAAC2B,IAAI,CAAClC,EAAE,CAAE;cAAAwB,QAAA,EAEvCU,IAAI,CAACjC;YAAK;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC,GAbJE,IAAI,CAAClC,EAAE;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcH,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEpB,CAAC;AAACtC,EAAA,CAzHIF,MAAM;AAAAsD,EAAA,GAANtD,MAAM;AA2HZ,eAAeA,MAAM;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}