import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaDownload, FaEnvelope, FaLinkedin, FaGoogle, FaGraduationCap, FaArrowRight } from 'react-icons/fa';
import ProfileImage from '../components/ProfileImage';
import '../styles/Home.css';

const Home = () => {
  const navigationCards = [
    {
      title: "About Me",
      description: "Learn about my background, expertise, and academic journey",
      icon: "👨‍🏫",
      link: "/about",
      color: "from-blue-500 to-purple-600"
    },
    {
      title: "Research",
      description: "Explore my research areas, current projects, and innovations",
      icon: "🔬",
      link: "/research",
      color: "from-green-500 to-teal-600"
    },
    {
      title: "Publications",
      description: "Browse my academic publications and research papers",
      icon: "📚",
      link: "/publications",
      color: "from-orange-500 to-red-600"
    },
    {
      title: "Teaching",
      description: "Discover my courses, teaching philosophy, and achievements",
      icon: "🎓",
      link: "/teaching",
      color: "from-purple-500 to-pink-600"
    },
    {
      title: "Contact",
      description: "Get in touch for collaborations and opportunities",
      icon: "📧",
      link: "/contact",
      color: "from-indigo-500 to-blue-600"
    }
  ];

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-background">
          <div className="hero-particles"></div>
        </div>
        
        <div className="container">
          <div className="hero-content">
            <motion.div
              className="hero-text"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <motion.h1
                className="hero-title"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                Ms. <span className="gradient-text">Hymavathi Thottathyl</span>
              </motion.h1>
              
              <motion.p
                className="hero-subtitle"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                Assistant Professor & PhD Scholar in Computer Science
              </motion.p>
              
              <motion.p
                className="hero-description"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                Specializing in artificial intelligence, machine learning, and data science with focus on
                breast cancer gene prediction and network security. Passionate about research and education.
              </motion.p>
              
              <motion.div
                className="hero-buttons"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.0 }}
              >
                <Link to="/contact" className="btn btn-primary">
                  <FaEnvelope />
                  Get In Touch
                </Link>
                <button className="btn btn-outline">
                  <FaDownload />
                  Download CV
                </button>
              </motion.div>
              
              <motion.div
                className="hero-social"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.2 }}
              >
                <a href="#" className="social-link" aria-label="LinkedIn">
                  <FaLinkedin />
                </a>
                <a href="#" className="social-link" aria-label="Google Scholar">
                  <FaGoogle />
                </a>
                <a href="#" className="social-link" aria-label="Academic Profile">
                  <FaGraduationCap />
                </a>
              </motion.div>
            </motion.div>
            
            <motion.div
              className="hero-image"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <div className="image-container">
                <div className="profile-image">
                  <img
                    src="/images/hymavathi.jpg"
                    alt="Ms. Hymavathi Thottathyl"
                    className="profile-photo"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.parentElement.innerHTML = `
                        <div style="width: 100%; height: 100%; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem;">
                          👩‍🏫
                        </div>
                      `;
                    }}
                  />
                </div>
                <div className="image-decoration"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Navigation Cards Section */}
      <section className="navigation-section">
        <div className="container">
          <motion.h2
            className="section-title"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Explore My Academic Portfolio
          </motion.h2>
          
          <div className="navigation-grid">
            {navigationCards.map((card, index) => (
              <motion.div
                key={card.title}
                className="navigation-card"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Link to={card.link} className="card-link">
                  <div className={`card-gradient bg-gradient-to-br ${card.color}`}>
                    <div className="card-content">
                      <div className="card-icon">{card.icon}</div>
                      <h3 className="card-title">{card.title}</h3>
                      <p className="card-description">{card.description}</p>
                      <div className="card-arrow">
                        <FaArrowRight />
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Quick Stats Section */}
      <section className="stats-section">
        <div className="container">
          <div className="stats-grid">
            <motion.div
              className="stat-item"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <div className="stat-number">4</div>
              <div className="stat-label">Publications</div>
            </motion.div>
            <motion.div
              className="stat-item"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="stat-number">1</div>
              <div className="stat-label">Patent</div>
            </motion.div>
            <motion.div
              className="stat-item"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="stat-number">10+</div>
              <div className="stat-label">Certifications</div>
            </motion.div>
            <motion.div
              className="stat-item"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <div className="stat-number">8+</div>
              <div className="stat-label">Workshops</div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
