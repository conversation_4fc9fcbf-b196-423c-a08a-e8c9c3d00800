{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\contexts\\\\ThemeContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext();\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s2();\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    // Check localStorage for saved theme preference\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      return savedTheme === 'dark';\n    }\n    // Check system preference\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  });\n  useEffect(() => {\n    // Save theme preference to localStorage\n    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');\n\n    // Apply theme to document root\n    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');\n\n    // Update CSS custom properties\n    const root = document.documentElement;\n    if (isDarkMode) {\n      // Dark mode colors\n      root.style.setProperty('--primary-color', '#64b5f6');\n      root.style.setProperty('--secondary-color', '#81c784');\n      root.style.setProperty('--accent-color', '#ffb74d');\n      root.style.setProperty('--background-primary', '#121212');\n      root.style.setProperty('--background-secondary', '#1e1e1e');\n      root.style.setProperty('--background-light', '#2d2d2d');\n      root.style.setProperty('--background-dark', '#0a0a0a');\n      root.style.setProperty('--text-primary', '#ffffff');\n      root.style.setProperty('--text-secondary', '#b0b0b0');\n      root.style.setProperty('--text-muted', '#808080');\n      root.style.setProperty('--border-color', '#404040');\n      root.style.setProperty('--shadow-light', '0 2px 10px rgba(0, 0, 0, 0.3)');\n      root.style.setProperty('--shadow-medium', '0 4px 20px rgba(0, 0, 0, 0.4)');\n      root.style.setProperty('--shadow-heavy', '0 8px 30px rgba(0, 0, 0, 0.5)');\n      root.style.setProperty('--gradient-primary', 'linear-gradient(135deg, #1a237e 0%, #3949ab 100%)');\n      root.style.setProperty('--gradient-secondary', 'linear-gradient(135deg, #2e7d32 0%, #66bb6a 100%)');\n    } else {\n      // Light mode colors\n      root.style.setProperty('--primary-color', '#2196f3');\n      root.style.setProperty('--secondary-color', '#4caf50');\n      root.style.setProperty('--accent-color', '#ff9800');\n      root.style.setProperty('--background-primary', '#ffffff');\n      root.style.setProperty('--background-secondary', '#f8f9fa');\n      root.style.setProperty('--background-light', '#f5f5f5');\n      root.style.setProperty('--background-dark', '#e0e0e0');\n      root.style.setProperty('--text-primary', '#333333');\n      root.style.setProperty('--text-secondary', '#666666');\n      root.style.setProperty('--text-muted', '#999999');\n      root.style.setProperty('--border-color', '#e0e0e0');\n      root.style.setProperty('--shadow-light', '0 2px 10px rgba(0, 0, 0, 0.1)');\n      root.style.setProperty('--shadow-medium', '0 4px 20px rgba(0, 0, 0, 0.15)');\n      root.style.setProperty('--shadow-heavy', '0 8px 30px rgba(0, 0, 0, 0.2)');\n      root.style.setProperty('--gradient-primary', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');\n      root.style.setProperty('--gradient-secondary', 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)');\n    }\n  }, [isDarkMode]);\n  const toggleTheme = () => {\n    setIsDarkMode(prev => !prev);\n  };\n  const value = {\n    isDarkMode,\n    toggleTheme,\n    theme: isDarkMode ? 'dark' : 'light'\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s2(ThemeProvider, \"aR4hiDcWS9PyXzuLPW8zlOjKhWo=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ThemeContext", "useTheme", "_s", "context", "Error", "ThemeProvider", "children", "_s2", "isDarkMode", "setIsDarkMode", "savedTheme", "localStorage", "getItem", "window", "matchMedia", "matches", "setItem", "document", "documentElement", "setAttribute", "root", "style", "setProperty", "toggleTheme", "prev", "value", "theme", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/contexts/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst ThemeContext = createContext();\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport const ThemeProvider = ({ children }) => {\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    // Check localStorage for saved theme preference\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      return savedTheme === 'dark';\n    }\n    // Check system preference\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  });\n\n  useEffect(() => {\n    // Save theme preference to localStorage\n    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');\n    \n    // Apply theme to document root\n    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');\n    \n    // Update CSS custom properties\n    const root = document.documentElement;\n    if (isDarkMode) {\n      // Dark mode colors\n      root.style.setProperty('--primary-color', '#64b5f6');\n      root.style.setProperty('--secondary-color', '#81c784');\n      root.style.setProperty('--accent-color', '#ffb74d');\n      root.style.setProperty('--background-primary', '#121212');\n      root.style.setProperty('--background-secondary', '#1e1e1e');\n      root.style.setProperty('--background-light', '#2d2d2d');\n      root.style.setProperty('--background-dark', '#0a0a0a');\n      root.style.setProperty('--text-primary', '#ffffff');\n      root.style.setProperty('--text-secondary', '#b0b0b0');\n      root.style.setProperty('--text-muted', '#808080');\n      root.style.setProperty('--border-color', '#404040');\n      root.style.setProperty('--shadow-light', '0 2px 10px rgba(0, 0, 0, 0.3)');\n      root.style.setProperty('--shadow-medium', '0 4px 20px rgba(0, 0, 0, 0.4)');\n      root.style.setProperty('--shadow-heavy', '0 8px 30px rgba(0, 0, 0, 0.5)');\n      root.style.setProperty('--gradient-primary', 'linear-gradient(135deg, #1a237e 0%, #3949ab 100%)');\n      root.style.setProperty('--gradient-secondary', 'linear-gradient(135deg, #2e7d32 0%, #66bb6a 100%)');\n    } else {\n      // Light mode colors\n      root.style.setProperty('--primary-color', '#2196f3');\n      root.style.setProperty('--secondary-color', '#4caf50');\n      root.style.setProperty('--accent-color', '#ff9800');\n      root.style.setProperty('--background-primary', '#ffffff');\n      root.style.setProperty('--background-secondary', '#f8f9fa');\n      root.style.setProperty('--background-light', '#f5f5f5');\n      root.style.setProperty('--background-dark', '#e0e0e0');\n      root.style.setProperty('--text-primary', '#333333');\n      root.style.setProperty('--text-secondary', '#666666');\n      root.style.setProperty('--text-muted', '#999999');\n      root.style.setProperty('--border-color', '#e0e0e0');\n      root.style.setProperty('--shadow-light', '0 2px 10px rgba(0, 0, 0, 0.1)');\n      root.style.setProperty('--shadow-medium', '0 4px 20px rgba(0, 0, 0, 0.15)');\n      root.style.setProperty('--shadow-heavy', '0 8px 30px rgba(0, 0, 0, 0.2)');\n      root.style.setProperty('--gradient-primary', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');\n      root.style.setProperty('--gradient-secondary', 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)');\n    }\n  }, [isDarkMode]);\n\n  const toggleTheme = () => {\n    setIsDarkMode(prev => !prev);\n  };\n\n  const value = {\n    isDarkMode,\n    toggleTheme,\n    theme: isDarkMode ? 'dark' : 'light'\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,gBAAGN,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMO,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGR,UAAU,CAACK,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAQrB,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,MAAM;IACjD;IACA,MAAMc,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,IAAIF,UAAU,EAAE;MACd,OAAOA,UAAU,KAAK,MAAM;IAC9B;IACA;IACA,OAAOG,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;EAClE,CAAC,CAAC;EAEFlB,SAAS,CAAC,MAAM;IACd;IACAc,YAAY,CAACK,OAAO,CAAC,OAAO,EAAER,UAAU,GAAG,MAAM,GAAG,OAAO,CAAC;;IAE5D;IACAS,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC,YAAY,EAAEX,UAAU,GAAG,MAAM,GAAG,OAAO,CAAC;;IAElF;IACA,MAAMY,IAAI,GAAGH,QAAQ,CAACC,eAAe;IACrC,IAAIV,UAAU,EAAE;MACd;MACAY,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAE,SAAS,CAAC;MACpDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAE,SAAS,CAAC;MACtDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,SAAS,CAAC;MACnDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAE,SAAS,CAAC;MACzDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,wBAAwB,EAAE,SAAS,CAAC;MAC3DF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAE,SAAS,CAAC;MACvDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAE,SAAS,CAAC;MACtDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,SAAS,CAAC;MACnDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,kBAAkB,EAAE,SAAS,CAAC;MACrDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,cAAc,EAAE,SAAS,CAAC;MACjDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,SAAS,CAAC;MACnDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,+BAA+B,CAAC;MACzEF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAE,+BAA+B,CAAC;MAC1EF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,+BAA+B,CAAC;MACzEF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAE,mDAAmD,CAAC;MACjGF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAE,mDAAmD,CAAC;IACrG,CAAC,MAAM;MACL;MACAF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAE,SAAS,CAAC;MACpDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAE,SAAS,CAAC;MACtDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,SAAS,CAAC;MACnDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAE,SAAS,CAAC;MACzDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,wBAAwB,EAAE,SAAS,CAAC;MAC3DF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAE,SAAS,CAAC;MACvDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAE,SAAS,CAAC;MACtDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,SAAS,CAAC;MACnDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,kBAAkB,EAAE,SAAS,CAAC;MACrDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,cAAc,EAAE,SAAS,CAAC;MACjDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,SAAS,CAAC;MACnDF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,+BAA+B,CAAC;MACzEF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAE,gCAAgC,CAAC;MAC3EF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,+BAA+B,CAAC;MACzEF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAE,mDAAmD,CAAC;MACjGF,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAE,mDAAmD,CAAC;IACrG;EACF,CAAC,EAAE,CAACd,UAAU,CAAC,CAAC;EAEhB,MAAMe,WAAW,GAAGA,CAAA,KAAM;IACxBd,aAAa,CAACe,IAAI,IAAI,CAACA,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMC,KAAK,GAAG;IACZjB,UAAU;IACVe,WAAW;IACXG,KAAK,EAAElB,UAAU,GAAG,MAAM,GAAG;EAC/B,CAAC;EAED,oBACET,OAAA,CAACC,YAAY,CAAC2B,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAnB,QAAA,EACjCA;EAAQ;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAACxB,GAAA,CA1EWF,aAAa;AAAA2B,EAAA,GAAb3B,aAAa;AAAA,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}