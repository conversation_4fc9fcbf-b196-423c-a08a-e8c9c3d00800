{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport LoadingScreen from './components/LoadingScreen';\nimport ThemeToggle from './components/ThemeToggle';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Research from './pages/Research';\nimport Publications from './pages/Publications';\nimport Teaching from './pages/Teaching';\nimport Contact from './pages/Contact';\nimport './styles/App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Simulate loading time\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 2000);\n    return () => clearTimeout(timer);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: loading && /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 23\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), !loading && /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ThemeToggle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 44\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/about\",\n                element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 48,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/research\",\n                element: /*#__PURE__*/_jsxDEV(Research, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/publications\",\n                element: /*#__PURE__*/_jsxDEV(Publications, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 56\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/teaching\",\n                element: /*#__PURE__*/_jsxDEV(Teaching, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/contact\",\n                element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "motion", "AnimatePresence", "ThemeProvider", "Header", "Footer", "LoadingScreen", "ThemeToggle", "Home", "About", "Research", "Publications", "Teaching", "Contact", "jsxDEV", "_jsxDEV", "App", "_s", "loading", "setLoading", "timer", "setTimeout", "clearTimeout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "animate", "transition", "duration", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport LoadingScreen from './components/LoadingScreen';\nimport ThemeToggle from './components/ThemeToggle';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Research from './pages/Research';\nimport Publications from './pages/Publications';\nimport Teaching from './pages/Teaching';\nimport Contact from './pages/Contact';\nimport './styles/App.css';\n\nfunction App() {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Simulate loading time\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 2000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <ThemeProvider>\n      <div className=\"App\">\n        <AnimatePresence>\n          {loading && <LoadingScreen />}\n        </AnimatePresence>\n\n        {!loading && (\n          <Router>\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              <Header />\n              <ThemeToggle />\n              <main>\n                <Routes>\n                  <Route path=\"/\" element={<Home />} />\n                  <Route path=\"/about\" element={<About />} />\n                  <Route path=\"/research\" element={<Research />} />\n                  <Route path=\"/publications\" element={<Publications />} />\n                  <Route path=\"/teaching\" element={<Teaching />} />\n                  <Route path=\"/contact\" element={<Contact />} />\n                </Routes>\n              </main>\n              <Footer />\n            </motion.div>\n          </Router>\n        )}\n      </div>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMwB,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEL,OAAA,CAACZ,aAAa;IAAAoB,QAAA,eACZR,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBR,OAAA,CAACb,eAAe;QAAAqB,QAAA,EACbL,OAAO,iBAAIH,OAAA,CAACT,aAAa;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,EAEjB,CAACV,OAAO,iBACPH,OAAA,CAACjB,MAAM;QAAAyB,QAAA,eACLR,OAAA,CAACd,MAAM,CAAC4B,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAX,QAAA,gBAE9BR,OAAA,CAACX,MAAM;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVb,OAAA,CAACR,WAAW;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACfb,OAAA;YAAAQ,QAAA,eACER,OAAA,CAAChB,MAAM;cAAAwB,QAAA,gBACLR,OAAA,CAACf,KAAK;gBAACmC,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAErB,OAAA,CAACP,IAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrCb,OAAA,CAACf,KAAK;gBAACmC,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAErB,OAAA,CAACN,KAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3Cb,OAAA,CAACf,KAAK;gBAACmC,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAErB,OAAA,CAACL,QAAQ;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDb,OAAA,CAACf,KAAK;gBAACmC,IAAI,EAAC,eAAe;gBAACC,OAAO,eAAErB,OAAA,CAACJ,YAAY;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDb,OAAA,CAACf,KAAK;gBAACmC,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAErB,OAAA,CAACH,QAAQ;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDb,OAAA,CAACf,KAAK;gBAACmC,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAErB,OAAA,CAACF,OAAO;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACPb,OAAA,CAACV,MAAM;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACX,EAAA,CA7CQD,GAAG;AAAAqB,EAAA,GAAHrB,GAAG;AA+CZ,eAAeA,GAAG;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}