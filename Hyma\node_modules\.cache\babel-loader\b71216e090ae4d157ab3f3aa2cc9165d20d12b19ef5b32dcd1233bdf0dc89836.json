{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\components\\\\Publications.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBook, FaExternalLinkAlt, FaQuoteLeft, FaFilter, FaSearch } from 'react-icons/fa';\nimport '../styles/Publications.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Publications = () => {\n  _s();\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const categories = [{\n    id: 'all',\n    label: 'All Publications'\n  }, {\n    id: 'journal',\n    label: 'Journal Articles'\n  }, {\n    id: 'conference',\n    label: 'Conference Papers'\n  }, {\n    id: 'book',\n    label: 'Books & Chapters'\n  }];\n  const publications = [{\n    id: 1,\n    title: \"Deep Learning Approaches for Autonomous Medical Diagnosis\",\n    authors: \"Dr. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\",\n    journal: \"Nature Machine Intelligence\",\n    year: 2023,\n    category: \"journal\",\n    citations: 156,\n    impact: \"High Impact\",\n    abstract: \"This paper presents novel deep learning methodologies for autonomous medical diagnosis, achieving 95% accuracy in early cancer detection.\",\n    doi: \"10.1038/s42256-023-00123-4\"\n  }, {\n    id: 2,\n    title: \"Ethical Frameworks for AI Decision Making\",\n    authors: \"Dr. Academic, A. Wilson, R. Brown\",\n    journal: \"Proceedings of ICML 2023\",\n    year: 2023,\n    category: \"conference\",\n    citations: 89,\n    impact: \"Medium Impact\",\n    abstract: \"We propose a comprehensive ethical framework for AI systems that ensures fairness, transparency, and accountability in automated decision-making processes.\",\n    doi: \"10.48550/arXiv.2023.12345\"\n  }, {\n    id: 3,\n    title: \"Machine Learning in Healthcare: A Comprehensive Guide\",\n    authors: \"Dr. Academic, K. Davis\",\n    journal: \"Academic Press\",\n    year: 2022,\n    category: \"book\",\n    citations: 234,\n    impact: \"High Impact\",\n    abstract: \"A comprehensive textbook covering the application of machine learning techniques in healthcare, from basic concepts to advanced implementations.\",\n    doi: \"978-0-12-345678-9\"\n  }, {\n    id: 4,\n    title: \"Reinforcement Learning for Robotics Applications\",\n    authors: \"Dr. Academic, L. Martinez, P. Anderson\",\n    journal: \"IEEE Transactions on Robotics\",\n    year: 2022,\n    category: \"journal\",\n    citations: 178,\n    impact: \"High Impact\",\n    abstract: \"This work demonstrates the application of reinforcement learning algorithms in complex robotics scenarios, improving task completion rates by 40%.\",\n    doi: \"10.1109/TRO.2022.1234567\"\n  }, {\n    id: 5,\n    title: \"Natural Language Processing for Scientific Literature Analysis\",\n    authors: \"Dr. Academic, S. Thompson, C. Lee\",\n    journal: \"Proceedings of ACL 2022\",\n    year: 2022,\n    category: \"conference\",\n    citations: 92,\n    impact: \"Medium Impact\",\n    abstract: \"We present a novel NLP framework for analyzing scientific literature, enabling automated extraction of research trends and knowledge gaps.\",\n    doi: \"10.18653/v1/2022.acl-long.123\"\n  }];\n  const filteredPublications = publications.filter(pub => {\n    const matchesCategory = selectedCategory === 'all' || pub.category === selectedCategory;\n    const matchesSearch = pub.title.toLowerCase().includes(searchTerm.toLowerCase()) || pub.authors.toLowerCase().includes(searchTerm.toLowerCase()) || pub.journal.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesCategory && matchesSearch;\n  });\n  const totalCitations = publications.reduce((sum, pub) => sum + pub.citations, 0);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"publications\",\n    className: \"section publications\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n        className: \"section-title\",\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        viewport: {\n          once: true\n        },\n        children: \"Publications & Research Output\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"publication-stats\",\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        viewport: {\n          once: true\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: publications.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Publications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: totalCitations\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Total Citations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"42\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"H-Index\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"15\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Years Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"publication-controls\",\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        viewport: {\n          once: true\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-box\",\n          children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n            className: \"search-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search publications...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"category-filters\",\n          children: [/*#__PURE__*/_jsxDEV(FaFilter, {\n            className: \"filter-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `filter-btn ${selectedCategory === category.id ? 'active' : ''}`,\n            onClick: () => setSelectedCategory(category.id),\n            children: category.label\n          }, category.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"publications-list\",\n        children: filteredPublications.map((publication, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"publication-card\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          viewport: {\n            once: true\n          },\n          whileHover: {\n            y: -5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"publication-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"publication-icon\",\n              children: /*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"publication-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `impact-badge ${publication.impact.toLowerCase().replace(' ', '-')}`,\n                children: publication.impact\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"publication-year\",\n                children: publication.year\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"publication-title\",\n            children: publication.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"publication-authors\",\n            children: publication.authors\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"publication-journal\",\n            children: publication.journal\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"publication-abstract\",\n            children: [/*#__PURE__*/_jsxDEV(FaQuoteLeft, {\n              className: \"quote-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: publication.abstract\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"publication-footer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"publication-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"citations\",\n                children: [publication.citations, \" citations\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"doi\",\n                children: [\"DOI: \", publication.doi]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline publication-link\",\n              children: [/*#__PURE__*/_jsxDEV(FaExternalLinkAlt, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this), \"View Paper\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, publication.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), filteredPublications.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"no-results\",\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No publications found matching your criteria.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(Publications, \"oSzvDQd7hFA8YV3r3I9HwCAFkfg=\");\n_c = Publications;\nexport default Publications;\nvar _c;\n$RefreshReg$(_c, \"Publications\");", "map": {"version": 3, "names": ["React", "useState", "motion", "FaBook", "FaExternalLinkAlt", "FaQuoteLeft", "FaFilter", "FaSearch", "jsxDEV", "_jsxDEV", "Publications", "_s", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "searchTerm", "setSearchTerm", "categories", "id", "label", "publications", "title", "authors", "journal", "year", "category", "citations", "impact", "abstract", "doi", "filteredPublications", "filter", "pub", "matchesCategory", "matchesSearch", "toLowerCase", "includes", "totalCitations", "reduce", "sum", "className", "children", "h2", "initial", "opacity", "y", "whileInView", "transition", "duration", "viewport", "once", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "delay", "length", "type", "placeholder", "value", "onChange", "e", "target", "map", "onClick", "publication", "index", "whileHover", "replace", "animate", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/components/Publications.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBook, FaExternalLinkAlt, FaQuoteLeft, FaFilter, FaSearch } from 'react-icons/fa';\nimport '../styles/Publications.css';\n\nconst Publications = () => {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  const categories = [\n    { id: 'all', label: 'All Publications' },\n    { id: 'journal', label: 'Journal Articles' },\n    { id: 'conference', label: 'Conference Papers' },\n    { id: 'book', label: 'Books & Chapters' }\n  ];\n\n  const publications = [\n    {\n      id: 1,\n      title: \"Deep Learning Approaches for Autonomous Medical Diagnosis\",\n      authors: \"Dr. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\",\n      journal: \"Nature Machine Intelligence\",\n      year: 2023,\n      category: \"journal\",\n      citations: 156,\n      impact: \"High Impact\",\n      abstract: \"This paper presents novel deep learning methodologies for autonomous medical diagnosis, achieving 95% accuracy in early cancer detection.\",\n      doi: \"10.1038/s42256-023-00123-4\"\n    },\n    {\n      id: 2,\n      title: \"Ethical Frameworks for AI Decision Making\",\n      authors: \"Dr. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\",\n      journal: \"Proceedings of ICML 2023\",\n      year: 2023,\n      category: \"conference\",\n      citations: 89,\n      impact: \"Medium Impact\",\n      abstract: \"We propose a comprehensive ethical framework for AI systems that ensures fairness, transparency, and accountability in automated decision-making processes.\",\n      doi: \"10.48550/arXiv.2023.12345\"\n    },\n    {\n      id: 3,\n      title: \"Machine Learning in Healthcare: A Comprehensive Guide\",\n      authors: \"Dr. Academic, K. Davis\",\n      journal: \"Academic Press\",\n      year: 2022,\n      category: \"book\",\n      citations: 234,\n      impact: \"High Impact\",\n      abstract: \"A comprehensive textbook covering the application of machine learning techniques in healthcare, from basic concepts to advanced implementations.\",\n      doi: \"978-0-12-345678-9\"\n    },\n    {\n      id: 4,\n      title: \"Reinforcement Learning for Robotics Applications\",\n      authors: \"Dr. Academic, L. Martinez, P. Anderson\",\n      journal: \"IEEE Transactions on Robotics\",\n      year: 2022,\n      category: \"journal\",\n      citations: 178,\n      impact: \"High Impact\",\n      abstract: \"This work demonstrates the application of reinforcement learning algorithms in complex robotics scenarios, improving task completion rates by 40%.\",\n      doi: \"10.1109/TRO.2022.1234567\"\n    },\n    {\n      id: 5,\n      title: \"Natural Language Processing for Scientific Literature Analysis\",\n      authors: \"Dr. Academic, S. Thompson, C. Lee\",\n      journal: \"Proceedings of ACL 2022\",\n      year: 2022,\n      category: \"conference\",\n      citations: 92,\n      impact: \"Medium Impact\",\n      abstract: \"We present a novel NLP framework for analyzing scientific literature, enabling automated extraction of research trends and knowledge gaps.\",\n      doi: \"10.18653/v1/2022.acl-long.123\"\n    }\n  ];\n\n  const filteredPublications = publications.filter(pub => {\n    const matchesCategory = selectedCategory === 'all' || pub.category === selectedCategory;\n    const matchesSearch = pub.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         pub.authors.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         pub.journal.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesCategory && matchesSearch;\n  });\n\n  const totalCitations = publications.reduce((sum, pub) => sum + pub.citations, 0);\n\n  return (\n    <section id=\"publications\" className=\"section publications\">\n      <div className=\"container\">\n        <motion.h2\n          className=\"section-title\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n        >\n          Publications & Research Output\n        </motion.h2>\n\n        {/* Publication Stats */}\n        <motion.div\n          className=\"publication-stats\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">{publications.length}</div>\n            <div className=\"stat-label\">Publications</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">{totalCitations}</div>\n            <div className=\"stat-label\">Total Citations</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">42</div>\n            <div className=\"stat-label\">H-Index</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">15</div>\n            <div className=\"stat-label\">Years Active</div>\n          </div>\n        </motion.div>\n\n        {/* Filters and Search */}\n        <motion.div\n          className=\"publication-controls\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"search-box\">\n            <FaSearch className=\"search-icon\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search publications...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n          \n          <div className=\"category-filters\">\n            <FaFilter className=\"filter-icon\" />\n            {categories.map(category => (\n              <button\n                key={category.id}\n                className={`filter-btn ${selectedCategory === category.id ? 'active' : ''}`}\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.label}\n              </button>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Publications List */}\n        <div className=\"publications-list\">\n          {filteredPublications.map((publication, index) => (\n            <motion.div\n              key={publication.id}\n              className=\"publication-card\"\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              whileHover={{ y: -5 }}\n            >\n              <div className=\"publication-header\">\n                <div className=\"publication-icon\">\n                  <FaBook />\n                </div>\n                <div className=\"publication-meta\">\n                  <span className={`impact-badge ${publication.impact.toLowerCase().replace(' ', '-')}`}>\n                    {publication.impact}\n                  </span>\n                  <span className=\"publication-year\">{publication.year}</span>\n                </div>\n              </div>\n\n              <h3 className=\"publication-title\">{publication.title}</h3>\n              <p className=\"publication-authors\">{publication.authors}</p>\n              <p className=\"publication-journal\">{publication.journal}</p>\n\n              <div className=\"publication-abstract\">\n                <FaQuoteLeft className=\"quote-icon\" />\n                <p>{publication.abstract}</p>\n              </div>\n\n              <div className=\"publication-footer\">\n                <div className=\"publication-stats\">\n                  <span className=\"citations\">{publication.citations} citations</span>\n                  <span className=\"doi\">DOI: {publication.doi}</span>\n                </div>\n                <button className=\"btn btn-outline publication-link\">\n                  <FaExternalLinkAlt />\n                  View Paper\n                </button>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {filteredPublications.length === 0 && (\n          <motion.div\n            className=\"no-results\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.5 }}\n          >\n            <p>No publications found matching your criteria.</p>\n          </motion.div>\n        )}\n      </div>\n    </section>\n  );\n};\n\nexport default Publications;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AAC3F,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMe,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAmB,CAAC,EACxC;IAAED,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAmB,CAAC,EAC5C;IAAED,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAoB,CAAC,EAChD;IAAED,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAmB,CAAC,CAC1C;EAED,MAAMC,YAAY,GAAG,CACnB;IACEF,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,2DAA2D;IAClEC,OAAO,EAAE,oCAAoC;IAC7CC,OAAO,EAAE,6BAA6B;IACtCC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,GAAG;IACdC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,2IAA2I;IACrJC,GAAG,EAAE;EACP,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,2CAA2C;IAClDC,OAAO,EAAE,mCAAmC;IAC5CC,OAAO,EAAE,0BAA0B;IACnCC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,EAAE;IACbC,MAAM,EAAE,eAAe;IACvBC,QAAQ,EAAE,6JAA6J;IACvKC,GAAG,EAAE;EACP,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,uDAAuD;IAC9DC,OAAO,EAAE,wBAAwB;IACjCC,OAAO,EAAE,gBAAgB;IACzBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,GAAG;IACdC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,kJAAkJ;IAC5JC,GAAG,EAAE;EACP,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,kDAAkD;IACzDC,OAAO,EAAE,wCAAwC;IACjDC,OAAO,EAAE,+BAA+B;IACxCC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,GAAG;IACdC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,oJAAoJ;IAC9JC,GAAG,EAAE;EACP,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,gEAAgE;IACvEC,OAAO,EAAE,mCAAmC;IAC5CC,OAAO,EAAE,yBAAyB;IAClCC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,EAAE;IACbC,MAAM,EAAE,eAAe;IACvBC,QAAQ,EAAE,4IAA4I;IACtJC,GAAG,EAAE;EACP,CAAC,CACF;EAED,MAAMC,oBAAoB,GAAGV,YAAY,CAACW,MAAM,CAACC,GAAG,IAAI;IACtD,MAAMC,eAAe,GAAGpB,gBAAgB,KAAK,KAAK,IAAImB,GAAG,CAACP,QAAQ,KAAKZ,gBAAgB;IACvF,MAAMqB,aAAa,GAAGF,GAAG,CAACX,KAAK,CAACc,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CAAC,IAC3DH,GAAG,CAACV,OAAO,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CAAC,IAC5DH,GAAG,CAACT,OAAO,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CAAC;IACjF,OAAOF,eAAe,IAAIC,aAAa;EACzC,CAAC,CAAC;EAEF,MAAMG,cAAc,GAAGjB,YAAY,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEP,GAAG,KAAKO,GAAG,GAAGP,GAAG,CAACN,SAAS,EAAE,CAAC,CAAC;EAEhF,oBACEhB,OAAA;IAASQ,EAAE,EAAC,cAAc;IAACsB,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACzD/B,OAAA;MAAK8B,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB/B,OAAA,CAACP,MAAM,CAACuC,EAAE;QACRF,SAAS,EAAC,eAAe;QACzBG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BC,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QAAAT,QAAA,EAC1B;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAGZ5C,OAAA,CAACP,MAAM,CAACoD,GAAG;QACTf,SAAS,EAAC,mBAAmB;QAC7BG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEQ,KAAK,EAAE;QAAI,CAAE;QAC1CP,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QAAAT,QAAA,gBAEzB/B,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/B,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAErB,YAAY,CAACqC;UAAM;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxD5C,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACN5C,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/B,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEJ;UAAc;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnD5C,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAe;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACN5C,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/B,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC5C,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAO;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACN5C,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/B,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC5C,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb5C,OAAA,CAACP,MAAM,CAACoD,GAAG;QACTf,SAAS,EAAC,sBAAsB;QAChCG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEQ,KAAK,EAAE;QAAI,CAAE;QAC1CP,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QAAAT,QAAA,gBAEzB/B,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/B,OAAA,CAACF,QAAQ;YAACgC,SAAS,EAAC;UAAa;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpC5C,OAAA;YACEgD,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,wBAAwB;YACpCC,KAAK,EAAE7C,UAAW;YAClB8C,QAAQ,EAAGC,CAAC,IAAK9C,aAAa,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CpB,SAAS,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5C,OAAA;UAAK8B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/B,OAAA,CAACH,QAAQ;YAACiC,SAAS,EAAC;UAAa;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnCrC,UAAU,CAAC+C,GAAG,CAACvC,QAAQ,iBACtBf,OAAA;YAEE8B,SAAS,EAAE,cAAc3B,gBAAgB,KAAKY,QAAQ,CAACP,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC5E+C,OAAO,EAAEA,CAAA,KAAMnD,mBAAmB,CAACW,QAAQ,CAACP,EAAE,CAAE;YAAAuB,QAAA,EAE/ChB,QAAQ,CAACN;UAAK,GAJVM,QAAQ,CAACP,EAAE;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKV,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb5C,OAAA;QAAK8B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC/BX,oBAAoB,CAACkC,GAAG,CAAC,CAACE,WAAW,EAAEC,KAAK,kBAC3CzD,OAAA,CAACP,MAAM,CAACoD,GAAG;UAETf,SAAS,EAAC,kBAAkB;UAC5BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEQ,KAAK,EAAEW,KAAK,GAAG;UAAI,CAAE;UAClDlB,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBkB,UAAU,EAAE;YAAEvB,CAAC,EAAE,CAAC;UAAE,CAAE;UAAAJ,QAAA,gBAEtB/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC/B,OAAA;cAAK8B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/B/B,OAAA,CAACN,MAAM;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACN5C,OAAA;cAAK8B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B/B,OAAA;gBAAM8B,SAAS,EAAE,gBAAgB0B,WAAW,CAACvC,MAAM,CAACQ,WAAW,CAAC,CAAC,CAACkC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAG;gBAAA5B,QAAA,EACnFyB,WAAW,CAACvC;cAAM;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACP5C,OAAA;gBAAM8B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEyB,WAAW,CAAC1C;cAAI;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5C,OAAA;YAAI8B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAEyB,WAAW,CAAC7C;UAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1D5C,OAAA;YAAG8B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEyB,WAAW,CAAC5C;UAAO;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5D5C,OAAA;YAAG8B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEyB,WAAW,CAAC3C;UAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE5D5C,OAAA;YAAK8B,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/B,OAAA,CAACJ,WAAW;cAACkC,SAAS,EAAC;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtC5C,OAAA;cAAA+B,QAAA,EAAIyB,WAAW,CAACtC;YAAQ;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAEN5C,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC/B,OAAA;cAAK8B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC/B,OAAA;gBAAM8B,SAAS,EAAC,WAAW;gBAAAC,QAAA,GAAEyB,WAAW,CAACxC,SAAS,EAAC,YAAU;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpE5C,OAAA;gBAAM8B,SAAS,EAAC,KAAK;gBAAAC,QAAA,GAAC,OAAK,EAACyB,WAAW,CAACrC,GAAG;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACN5C,OAAA;cAAQ8B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAClD/B,OAAA,CAACL,iBAAiB;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAEvB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GAtCDY,WAAW,CAAChD,EAAE;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuCT,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELxB,oBAAoB,CAAC2B,MAAM,KAAK,CAAC,iBAChC/C,OAAA,CAACP,MAAM,CAACoD,GAAG;QACTf,SAAS,EAAC,YAAY;QACtBG,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxB0B,OAAO,EAAE;UAAE1B,OAAO,EAAE;QAAE,CAAE;QACxBG,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAP,QAAA,eAE9B/B,OAAA;UAAA+B,QAAA,EAAG;QAA6C;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC1C,EAAA,CAxNID,YAAY;AAAA4D,EAAA,GAAZ5D,YAAY;AA0NlB,eAAeA,YAAY;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}