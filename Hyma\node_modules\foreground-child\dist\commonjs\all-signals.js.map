{"version": 3, "file": "all-signals.js", "sourceRoot": "", "sources": ["../../src/all-signals.ts"], "names": [], "mappings": ";;;;;;AAAA,oEAAsC;AACzB,QAAA,UAAU;AACrB,0EAA0E;AAC1E,MAAM,CAAC,IAAI,CAAC,wBAAS,CAAC,CAAC,MAAM,CAC3B,CAAC,CAAC,EAAE,CACF,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC;IACnB,iDAAiD;IACjD,CAAC,KAAK,SAAS;IACf,yDAAyD;IACzD,CAAC,KAAK,SAAS,CACE,CAAA;AAEvB,8DAA8D;AAC9D,gEAAgE;AAChE,6DAA6D;AAC7D,gCAAgC;AAChC,EAAE;AACF,YAAY;AACZ,aAAa;AACb,aAAa;AACb,cAAc;AACd,gBAAgB;AAChB,iBAAiB;AACjB,iBAAiB;AACjB,iBAAiB;AACjB,iBAAiB;AACjB,iBAAiB;AACjB,iBAAiB;AACjB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,cAAc;AACd,gBAAgB;AAChB,iBAAiB;AACjB,iBAAiB;AACjB,iBAAiB;AACjB,iBAAiB;AACjB,iBAAiB;AACjB,iBAAiB;AACjB,iBAAiB;AACjB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,gBAAgB;AAChB,eAAe;AACf,eAAe", "sourcesContent": ["import constants from 'node:constants'\nexport const allSignals =\n  // this is the full list of signals that <PERSON><PERSON> will let us do anything with\n  Object.keys(constants).filter(\n    k =>\n      k.startsWith('SIG') &&\n      // https://github.com/tapjs/signal-exit/issues/21\n      k !== 'SIGPROF' &&\n      // no sense trying to listen for SIGK<PERSON><PERSON>, it's impossible\n      k !== 'SIGKILL',\n  ) as NodeJS.Signals[]\n\n// These are some obscure signals that are reported by kill -l\n// on macOS, Linux, or Windows, but which don't have any mapping\n// in Node.js. No sense trying if they're just going to throw\n// every time on every platform.\n//\n// 'SIGEMT',\n// 'SIGLOST',\n// 'SIGPOLL',\n// 'SIGRTMAX',\n// 'SIGRTMAX-1',\n// 'SIGRTMAX-10',\n// 'SIGRTMAX-11',\n// 'SIGRTMAX-12',\n// 'SIGRTMAX-13',\n// 'SIGRTMAX-14',\n// 'SIGRTMAX-15',\n// 'SIGRTMAX-2',\n// 'SIGRTMAX-3',\n// 'SIGRTMAX-4',\n// 'SIGRTMAX-5',\n// 'SIGRTMAX-6',\n// 'SIGRTMAX-7',\n// 'SIGRTMAX-8',\n// 'SIGRTMAX-9',\n// 'SIGRTMIN',\n// 'SIGRTMIN+1',\n// 'SIGRTMIN+10',\n// 'SIGRTMIN+11',\n// 'SIGRTMIN+12',\n// 'SIGRTMIN+13',\n// 'SIGRTMIN+14',\n// 'SIGRTMIN+15',\n// 'SIGRTMIN+16',\n// 'SIGRTMIN+2',\n// 'SIGRTMIN+3',\n// 'SIGRTMIN+4',\n// 'SIGRTMIN+5',\n// 'SIGRTMIN+6',\n// 'SIGRTMIN+7',\n// 'SIGRTMIN+8',\n// 'SIGRTMIN+9',\n// 'SIGSTKFLT',\n// 'SIGUNUSED',\n"]}