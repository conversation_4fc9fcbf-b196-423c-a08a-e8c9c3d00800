/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/// <reference types="node" />
import type { Config } from '@jest/types';
export default function logDebugMessages(globalConfig: Config.GlobalConfig, configs: Array<Config.ProjectConfig> | Config.ProjectConfig, outputStream: NodeJS.WriteStream): void;
