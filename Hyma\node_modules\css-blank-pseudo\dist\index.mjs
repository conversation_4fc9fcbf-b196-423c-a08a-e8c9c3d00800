import e from"postcss-selector-parser";const r=r=>{const s=String(Object(r).replaceWith||"[blank]"),t=e().astSync(s),o=Boolean(!("preserve"in Object(r))||r.preserve);return{postcssPlugin:"css-blank-pseudo",Rule:(r,{result:s})=>{if(-1===r.selector.indexOf(":blank"))return;let c;try{const s=e((e=>{e.walkPseudos((e=>{":blank"===e.value&&(e.nodes&&e.nodes.length||e.replaceWith(t.clone()))}))})).processSync(r.selector);c=String(s)}catch(e){return void r.warn(s,`Failed to parse selector : ${r.selector}`)}if(void 0===c)return;if(c===r.selector)return;const n=r.clone({selector:c});o?r.before(n):r.replaceWith(n)}}};r.postcss=!0;export{r as default};
