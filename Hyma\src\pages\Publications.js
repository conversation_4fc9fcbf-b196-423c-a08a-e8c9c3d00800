import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaBook, FaExternalLinkAlt, FaQuoteLeft, FaFilter, FaSearch, FaArrowLeft } from 'react-icons/fa';
import '../styles/Publications.css';
import '../styles/PageLayout.css';

const Publications = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = [
    { id: 'all', label: 'All Publications' },
    { id: 'journal', label: 'Journal Articles' },
    { id: 'conference', label: 'Conference Papers' },
    { id: 'patent', label: 'Patents' }
  ];

  const publications = [
    {
      id: 1,
      title: "Prediction of the Most Influenced Gene in the Development of Breast Cancer Using the DELSTM Model",
      authors: "<PERSON><PERSON><PERSON><PERSON>hyl, Kanadam Karteeka Pavan",
      journal: "Ingénierie des Systèmes d'Information",
      year: 2025,
      category: "journal",
      citations: 0,
      impact: "High Impact",
      abstract: "This paper presents a novel DELSTM model for predicting the most influenced genes in breast cancer development, contributing to early detection and treatment strategies.",
      doi: "Vol. 30, No. 1, pp. 279-286"
    },
    {
      id: 2,
      title: "Enhancing Cyber Attack Detection in Network Traffic Using Adaptive Regression Techniques",
      authors: "Dr. Talluri.Sunil Kumar, P. Jyothi, Dr. Rajesh Kumar Verma, Padmini Debbarma, Dr.N.V.S. Pavan Kumar, I. Naga Padmaja, Hymavathi Thottathyl, Dr.M. Sathish Kumar",
      journal: "Journal of Theoretical and Applied Information Technology",
      year: 2024,
      category: "journal",
      citations: 0,
      impact: "High Impact",
      abstract: "This research presents adaptive regression techniques for enhancing cyber attack detection in network traffic, improving security measures in modern networks.",
      doi: "Vol. 102, No. 23, pp. 8655-8664"
    },
    {
      id: 3,
      title: "Differential Evolution Model for Identification of Most Influenced Gene in Brest Cancer Data",
      authors: "Hymavathi Thottathyl, Kanadam Karteeka Pavan",
      journal: "Ingénierie des Systèmes d'Information",
      year: 2022,
      category: "journal",
      citations: 0,
      impact: "High Impact",
      abstract: "This paper presents a differential evolution model for identifying the most influenced genes in breast cancer data, providing insights for targeted therapy approaches.",
      doi: "Vol. 27, No. 3, pp. 487-493"
    },
    {
      id: 4,
      title: "Microarray Breast Cancer Data Clustering Using Map Reduce Based K-Means Algorithm",
      authors: "Hymavathi Thottathyl, Kanadam Karteeka Pavan, Rajeev Priyatam Panchadula",
      journal: "Revue d'Intelligence Artificielle",
      year: 2020,
      category: "journal",
      citations: 0,
      impact: "High Impact",
      abstract: "This research presents a Map Reduce based K-Means algorithm for clustering microarray breast cancer data, enabling efficient analysis of large-scale genomic datasets.",
      doi: "Vol. 34, No. 6, pp. 763-769"
    },
    {
      id: 5,
      title: "Artificial Intelligence (AI) Driven Personal Assistant with Multi-Platform Integration",
      authors: "Ms. Hymavathi.T et. al.",
      journal: "Indian Patent",
      year: 2023,
      category: "patent",
      citations: 0,
      impact: "High Impact",
      abstract: "Patent for an AI-driven personal assistant system with multi-platform integration capabilities, filed and published in 2023.",
      doi: "Application No. ************ A"
    }
  ];

  const filteredPublications = publications.filter(pub => {
    const matchesCategory = selectedCategory === 'all' || pub.category === selectedCategory;
    const matchesSearch = pub.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pub.authors.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pub.journal.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const totalCitations = publications.reduce((sum, pub) => sum + pub.citations, 0);

  return (
    <div className="page-container">
      {/* Page Header */}
      <motion.div
        className="page-header"
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container">
          <Link to="/" className="back-link">
            <FaArrowLeft />
            Back to Home
          </Link>
          <h1 className="page-title">Publications & Research Output</h1>
          <p className="page-subtitle">Browse my academic publications and research papers</p>
        </div>
      </motion.div>

      <section className="section publications">
        <div className="container">
          {/* Publication Stats */}
          <motion.div
            className="publication-stats"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="stat-item">
              <div className="stat-number">{publications.length}</div>
              <div className="stat-label">Publications</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">1</div>
              <div className="stat-label">Patent</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">SCOPUS</div>
              <div className="stat-label">Indexed</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">2020-2025</div>
              <div className="stat-label">Active Years</div>
            </div>
          </motion.div>

          {/* Filters and Search */}
          <motion.div
            className="publication-controls"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="search-box">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search publications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
            
            <div className="category-filters">
              <FaFilter className="filter-icon" />
              {categories.map(category => (
                <button
                  key={category.id}
                  className={`filter-btn ${selectedCategory === category.id ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.label}
                </button>
              ))}
            </div>
          </motion.div>

          {/* Publications List */}
          <div className="publications-list">
            {filteredPublications.map((publication, index) => (
              <motion.div
                key={publication.id}
                className="publication-card"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                whileHover={{ y: -5 }}
              >
                <div className="publication-header">
                  <div className="publication-icon">
                    <FaBook />
                  </div>
                  <div className="publication-meta">
                    <span className={`impact-badge ${publication.impact.toLowerCase().replace(' ', '-')}`}>
                      {publication.impact}
                    </span>
                    <span className="publication-year">{publication.year}</span>
                  </div>
                </div>

                <h3 className="publication-title">{publication.title}</h3>
                <p className="publication-authors">{publication.authors}</p>
                <p className="publication-journal">{publication.journal}</p>

                <div className="publication-abstract">
                  <FaQuoteLeft className="quote-icon" />
                  <p>{publication.abstract}</p>
                </div>

                <div className="publication-footer">
                  <div className="publication-stats">
                    <span className="citations">{publication.citations} citations</span>
                    <span className="doi">DOI: {publication.doi}</span>
                  </div>
                  <button className="btn btn-outline publication-link">
                    <FaExternalLinkAlt />
                    View Paper
                  </button>
                </div>
              </motion.div>
            ))}
          </div>

          {filteredPublications.length === 0 && (
            <motion.div
              className="no-results"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <p>No publications found matching your criteria.</p>
            </motion.div>
          )}
        </div>
      </section>
    </div>
  );
};

export default Publications;
