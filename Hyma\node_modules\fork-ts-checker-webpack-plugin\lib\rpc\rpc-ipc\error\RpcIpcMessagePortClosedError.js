"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const RpcMessagePortClosedError_1 = require("../../error/RpcMessagePortClosedError");
class RpcIpcMessagePortClosedError extends RpcMessagePortClosedError_1.RpcMessagePortClosedError {
    constructor(message, code, signal) {
        super(message);
        this.code = code;
        this.signal = signal;
        this.name = 'RpcIpcMessagePortClosedError';
    }
}
exports.RpcIpcMessagePortClosedError = RpcIpcMessagePortClosedError;
