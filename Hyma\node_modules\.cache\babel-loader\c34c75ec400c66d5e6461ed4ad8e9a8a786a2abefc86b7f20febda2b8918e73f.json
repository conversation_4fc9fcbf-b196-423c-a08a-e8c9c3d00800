{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\pages\\\\Research.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaRobot, FaBrain, FaEye, FaLanguage, FaChartLine, FaExternalLinkAlt, FaArrowLeft } from 'react-icons/fa';\nimport '../styles/Research.css';\nimport '../styles/PageLayout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Research = () => {\n  _s();\n  const [activeProject, setActiveProject] = useState(0);\n  const researchAreas = [{\n    icon: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 13\n    }, this),\n    title: \"Breast Cancer Gene Prediction\",\n    description: \"Using DELSTM and differential evolution models to predict influential genes in breast cancer development.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaBrain, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 13\n    }, this),\n    title: \"Machine Learning\",\n    description: \"Developing advanced ML algorithms for healthcare applications and data clustering techniques.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this),\n    title: \"Network Security\",\n    description: \"Enhancing cyber attack detection using adaptive regression techniques in network traffic analysis.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaLanguage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this),\n    title: \"Data Science\",\n    description: \"Applying data science methodologies to solve complex problems in healthcare and cybersecurity.\"\n  }];\n  const projects = [{\n    title: \"DELSTM Model for Breast Cancer Gene Prediction\",\n    description: \"Developing a novel DELSTM model to predict the most influenced genes in breast cancer development for early detection and treatment.\",\n    status: \"Published\",\n    funding: \"University Research Grant\",\n    collaborators: [\"Kanadam Karteeka Pavan\", \"Acharya Nagarjuna University\"],\n    publications: 2,\n    image: \"🧬\"\n  }, {\n    title: \"Cyber Attack Detection in Network Traffic\",\n    description: \"Enhancing network security through adaptive regression techniques for improved cyber attack detection capabilities.\",\n    status: \"Published\",\n    funding: \"Collaborative Research\",\n    collaborators: [\"Dr. Talluri Sunil Kumar\", \"Multiple Institutions\"],\n    publications: 1,\n    image: \"🔒\"\n  }, {\n    title: \"AI-Driven Personal Assistant\",\n    description: \"Developing an AI-driven personal assistant with multi-platform integration capabilities for enhanced user experience.\",\n    status: \"Patent Filed\",\n    funding: \"Innovation Grant\",\n    collaborators: [\"Research Team\"],\n    publications: 1,\n    image: \"🤖\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"page-header\",\n      initial: {\n        opacity: 0,\n        y: -30\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"back-link\",\n          children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: \"Research & Innovation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Explore my research areas, current projects, and innovations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section research\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"research-areas\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n            className: \"subsection-title\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            children: \"Research Areas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"areas-grid\",\n            children: researchAreas.map((area, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"area-card\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4 + index * 0.1\n              },\n              whileHover: {\n                y: -5\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"area-icon\",\n                children: area.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: area.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: area.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this)]\n            }, area.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"current-projects\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n            className: \"subsection-title\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.8\n            },\n            children: \"Current Projects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"projects-container\",\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 1.0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-tabs\",\n              children: projects.map((project, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `project-tab ${activeProject === index ? 'active' : ''}`,\n                onClick: () => setActiveProject(index),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"tab-icon\",\n                  children: project.image\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"tab-title\",\n                  children: project.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 21\n                }, this)]\n              }, project.title, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"project-content\",\n              initial: {\n                opacity: 0,\n                x: 20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                duration: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"project-icon\",\n                  children: projects[activeProject].image\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"project-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: projects[activeProject].title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `project-status ${projects[activeProject].status.toLowerCase()}`,\n                    children: projects[activeProject].status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"project-description\",\n                children: projects[activeProject].description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Funding:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 21\n                  }, this), \" \", projects[activeProject].funding]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Collaborators:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this), \" \", projects[activeProject].collaborators.join(', ')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Publications:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this), \" \", projects[activeProject].publications, \" papers\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-outline project-link\",\n                children: [/*#__PURE__*/_jsxDEV(FaExternalLinkAlt, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), \"View Project Details\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, activeProject, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"research-impact\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 1.2\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"impact-card\",\n            children: [/*#__PURE__*/_jsxDEV(FaChartLine, {\n              className: \"impact-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"impact-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Research Impact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"My research focuses on practical applications of AI in healthcare and cybersecurity, contributing to early cancer detection methods and enhanced network security protocols.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(Research, \"xvcqmwZVdQqhei3WzRdkmwqVXHU=\");\n_c = Research;\nexport default Research;\nvar _c;\n$RefreshReg$(_c, \"Research\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "FaRobot", "FaBrain", "FaEye", "FaLanguage", "FaChartLine", "FaExternalLinkAlt", "FaArrowLeft", "jsxDEV", "_jsxDEV", "Research", "_s", "activeProject", "setActiveProject", "researchAreas", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "projects", "status", "funding", "collaborators", "publications", "image", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "to", "h3", "delay", "map", "area", "index", "whileHover", "project", "onClick", "x", "toLowerCase", "join", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/pages/Research.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaRobot, FaBrain, FaEye, FaLanguage, FaChartLine, FaExternalLinkAlt, FaArrowLeft } from 'react-icons/fa';\nimport '../styles/Research.css';\nimport '../styles/PageLayout.css';\n\nconst Research = () => {\n  const [activeProject, setActiveProject] = useState(0);\n\n  const researchAreas = [\n    {\n      icon: <FaRobot />,\n      title: \"Breast Cancer Gene Prediction\",\n      description: \"Using DELSTM and differential evolution models to predict influential genes in breast cancer development.\"\n    },\n    {\n      icon: <FaBrain />,\n      title: \"Machine Learning\",\n      description: \"Developing advanced ML algorithms for healthcare applications and data clustering techniques.\"\n    },\n    {\n      icon: <FaEye />,\n      title: \"Network Security\",\n      description: \"Enhancing cyber attack detection using adaptive regression techniques in network traffic analysis.\"\n    },\n    {\n      icon: <FaLanguage />,\n      title: \"Data Science\",\n      description: \"Applying data science methodologies to solve complex problems in healthcare and cybersecurity.\"\n    }\n  ];\n\n  const projects = [\n    {\n      title: \"DELSTM Model for Breast Cancer Gene Prediction\",\n      description: \"Developing a novel DELSTM model to predict the most influenced genes in breast cancer development for early detection and treatment.\",\n      status: \"Published\",\n      funding: \"University Research Grant\",\n      collaborators: [\"Kanadam Karteeka Pavan\", \"Acharya Nagarjuna University\"],\n      publications: 2,\n      image: \"🧬\"\n    },\n    {\n      title: \"Cyber Attack Detection in Network Traffic\",\n      description: \"Enhancing network security through adaptive regression techniques for improved cyber attack detection capabilities.\",\n      status: \"Published\",\n      funding: \"Collaborative Research\",\n      collaborators: [\"Dr. Talluri Sunil Kumar\", \"Multiple Institutions\"],\n      publications: 1,\n      image: \"🔒\"\n    },\n    {\n      title: \"AI-Driven Personal Assistant\",\n      description: \"Developing an AI-driven personal assistant with multi-platform integration capabilities for enhanced user experience.\",\n      status: \"Patent Filed\",\n      funding: \"Innovation Grant\",\n      collaborators: [\"Research Team\"],\n      publications: 1,\n      image: \"🤖\"\n    }\n  ];\n\n  return (\n    <div className=\"page-container\">\n      {/* Page Header */}\n      <motion.div\n        className=\"page-header\"\n        initial={{ opacity: 0, y: -30 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <div className=\"container\">\n          <Link to=\"/\" className=\"back-link\">\n            <FaArrowLeft />\n            Back to Home\n          </Link>\n          <h1 className=\"page-title\">Research & Innovation</h1>\n          <p className=\"page-subtitle\">Explore my research areas, current projects, and innovations</p>\n        </div>\n      </motion.div>\n\n      <section className=\"section research\">\n        <div className=\"container\">\n          {/* Research Areas */}\n          <div className=\"research-areas\">\n            <motion.h3\n              className=\"subsection-title\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n            >\n              Research Areas\n            </motion.h3>\n            \n            <div className=\"areas-grid\">\n              {researchAreas.map((area, index) => (\n                <motion.div\n                  key={area.title}\n                  className=\"area-card\"\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}\n                  whileHover={{ y: -5 }}\n                >\n                  <div className=\"area-icon\">{area.icon}</div>\n                  <h4>{area.title}</h4>\n                  <p>{area.description}</p>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Current Projects */}\n          <div className=\"current-projects\">\n            <motion.h3\n              className=\"subsection-title\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.8 }}\n            >\n              Current Projects\n            </motion.h3>\n\n            <motion.div\n              className=\"projects-container\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.0 }}\n            >\n              <div className=\"project-tabs\">\n                {projects.map((project, index) => (\n                  <button\n                    key={project.title}\n                    className={`project-tab ${activeProject === index ? 'active' : ''}`}\n                    onClick={() => setActiveProject(index)}\n                  >\n                    <span className=\"tab-icon\">{project.image}</span>\n                    <span className=\"tab-title\">{project.title}</span>\n                  </button>\n                ))}\n              </div>\n\n              <motion.div\n                className=\"project-content\"\n                key={activeProject}\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.5 }}\n              >\n                <div className=\"project-header\">\n                  <div className=\"project-icon\">{projects[activeProject].image}</div>\n                  <div className=\"project-info\">\n                    <h4>{projects[activeProject].title}</h4>\n                    <span className={`project-status ${projects[activeProject].status.toLowerCase()}`}>\n                      {projects[activeProject].status}\n                    </span>\n                  </div>\n                </div>\n\n                <p className=\"project-description\">\n                  {projects[activeProject].description}\n                </p>\n\n                <div className=\"project-details\">\n                  <div className=\"detail-item\">\n                    <strong>Funding:</strong> {projects[activeProject].funding}\n                  </div>\n                  <div className=\"detail-item\">\n                    <strong>Collaborators:</strong> {projects[activeProject].collaborators.join(', ')}\n                  </div>\n                  <div className=\"detail-item\">\n                    <strong>Publications:</strong> {projects[activeProject].publications} papers\n                  </div>\n                </div>\n\n                <button className=\"btn btn-outline project-link\">\n                  <FaExternalLinkAlt />\n                  View Project Details\n                </button>\n              </motion.div>\n            </motion.div>\n          </div>\n\n          {/* Research Impact */}\n          <motion.div\n            className=\"research-impact\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n          >\n            <div className=\"impact-card\">\n              <FaChartLine className=\"impact-icon\" />\n              <div className=\"impact-content\">\n                <h4>Research Impact</h4>\n                <p>\n                  My research focuses on practical applications of AI in healthcare and cybersecurity,\n                  contributing to early cancer detection methods and enhanced network security protocols.\n                </p>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Research;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,gBAAgB;AACjH,OAAO,wBAAwB;AAC/B,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EAErD,MAAMgB,aAAa,GAAG,CACpB;IACEC,IAAI,eAAEN,OAAA,CAACR,OAAO;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,KAAK,EAAE,+BAA+B;IACtCC,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,eAAEN,OAAA,CAACP,OAAO;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,eAAEN,OAAA,CAACN,KAAK;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACfC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,eAAEN,OAAA,CAACL,UAAU;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG,CACf;IACEF,KAAK,EAAE,gDAAgD;IACvDC,WAAW,EAAE,sIAAsI;IACnJE,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE,2BAA2B;IACpCC,aAAa,EAAE,CAAC,wBAAwB,EAAE,8BAA8B,CAAC;IACzEC,YAAY,EAAE,CAAC;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,KAAK,EAAE,2CAA2C;IAClDC,WAAW,EAAE,qHAAqH;IAClIE,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE,wBAAwB;IACjCC,aAAa,EAAE,CAAC,yBAAyB,EAAE,uBAAuB,CAAC;IACnEC,YAAY,EAAE,CAAC;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,uHAAuH;IACpIE,MAAM,EAAE,cAAc;IACtBC,OAAO,EAAE,kBAAkB;IAC3BC,aAAa,EAAE,CAAC,eAAe,CAAC;IAChCC,YAAY,EAAE,CAAC;IACfC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACElB,OAAA;IAAKmB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7BpB,OAAA,CAACV,MAAM,CAAC+B,GAAG;MACTF,SAAS,EAAC,aAAa;MACvBG,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAP,QAAA,eAE9BpB,OAAA;QAAKmB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpB,OAAA,CAACT,IAAI;UAACqC,EAAE,EAAC,GAAG;UAACT,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAChCpB,OAAA,CAACF,WAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPV,OAAA;UAAImB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAqB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrDV,OAAA;UAAGmB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA4D;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEbV,OAAA;MAASmB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCpB,OAAA;QAAKmB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAExBpB,OAAA;UAAKmB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpB,OAAA,CAACV,MAAM,CAACuC,EAAE;YACRV,SAAS,EAAC,kBAAkB;YAC5BG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAAAV,QAAA,EAC3C;UAED;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAEZV,OAAA;YAAKmB,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxBf,aAAa,CAAC0B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7BjC,OAAA,CAACV,MAAM,CAAC+B,GAAG;cAETF,SAAS,EAAC,WAAW;cACrBG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE,GAAG,GAAGG,KAAK,GAAG;cAAI,CAAE;cACxDC,UAAU,EAAE;gBAAEV,CAAC,EAAE,CAAC;cAAE,CAAE;cAAAJ,QAAA,gBAEtBpB,OAAA;gBAAKmB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEY,IAAI,CAAC1B;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CV,OAAA;gBAAAoB,QAAA,EAAKY,IAAI,CAACrB;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBV,OAAA;gBAAAoB,QAAA,EAAIY,IAAI,CAACpB;cAAW;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GATpBsB,IAAI,CAACrB,KAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUL,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNV,OAAA;UAAKmB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BpB,OAAA,CAACV,MAAM,CAACuC,EAAE;YACRV,SAAS,EAAC,kBAAkB;YAC5BG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAAAV,QAAA,EAC3C;UAED;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAEZV,OAAA,CAACV,MAAM,CAAC+B,GAAG;YACTF,SAAS,EAAC,oBAAoB;YAC9BG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAAAV,QAAA,gBAE1CpB,OAAA;cAAKmB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAC1BP,QAAQ,CAACkB,GAAG,CAAC,CAACI,OAAO,EAAEF,KAAK,kBAC3BjC,OAAA;gBAEEmB,SAAS,EAAE,eAAehB,aAAa,KAAK8B,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACpEG,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC6B,KAAK,CAAE;gBAAAb,QAAA,gBAEvCpB,OAAA;kBAAMmB,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAEe,OAAO,CAACjB;gBAAK;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDV,OAAA;kBAAMmB,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEe,OAAO,CAACxB;gBAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAL7CyB,OAAO,CAACxB,KAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMZ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENV,OAAA,CAACV,MAAM,CAAC+B,GAAG;cACTF,SAAS,EAAC,iBAAiB;cAE3BG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/BZ,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BX,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAP,QAAA,gBAE9BpB,OAAA;gBAAKmB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BpB,OAAA;kBAAKmB,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEP,QAAQ,CAACV,aAAa,CAAC,CAACe;gBAAK;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnEV,OAAA;kBAAKmB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BpB,OAAA;oBAAAoB,QAAA,EAAKP,QAAQ,CAACV,aAAa,CAAC,CAACQ;kBAAK;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxCV,OAAA;oBAAMmB,SAAS,EAAE,kBAAkBN,QAAQ,CAACV,aAAa,CAAC,CAACW,MAAM,CAACwB,WAAW,CAAC,CAAC,EAAG;oBAAAlB,QAAA,EAC/EP,QAAQ,CAACV,aAAa,CAAC,CAACW;kBAAM;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENV,OAAA;gBAAGmB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAC/BP,QAAQ,CAACV,aAAa,CAAC,CAACS;cAAW;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEJV,OAAA;gBAAKmB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BpB,OAAA;kBAAKmB,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BpB,OAAA;oBAAAoB,QAAA,EAAQ;kBAAQ;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACG,QAAQ,CAACV,aAAa,CAAC,CAACY,OAAO;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACNV,OAAA;kBAAKmB,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BpB,OAAA;oBAAAoB,QAAA,EAAQ;kBAAc;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACG,QAAQ,CAACV,aAAa,CAAC,CAACa,aAAa,CAACuB,IAAI,CAAC,IAAI,CAAC;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACNV,OAAA;kBAAKmB,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BpB,OAAA;oBAAAoB,QAAA,EAAQ;kBAAa;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACG,QAAQ,CAACV,aAAa,CAAC,CAACc,YAAY,EAAC,SACvE;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENV,OAAA;gBAAQmB,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC9CpB,OAAA,CAACH,iBAAiB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBAEvB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAlCJP,aAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmCR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNV,OAAA,CAACV,MAAM,CAAC+B,GAAG;UACTF,SAAS,EAAC,iBAAiB;UAC3BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,eAE1CpB,OAAA;YAAKmB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BpB,OAAA,CAACJ,WAAW;cAACuB,SAAS,EAAC;YAAa;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCV,OAAA;cAAKmB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BpB,OAAA;gBAAAoB,QAAA,EAAI;cAAe;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBV,OAAA;gBAAAoB,QAAA,EAAG;cAGH;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACR,EAAA,CAvMID,QAAQ;AAAAuC,EAAA,GAARvC,QAAQ;AAyMd,eAAeA,QAAQ;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}