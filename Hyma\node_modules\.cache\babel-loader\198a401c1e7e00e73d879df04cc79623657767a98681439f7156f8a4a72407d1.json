{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport { FaBars, FaTimes } from 'react-icons/fa';\nimport '../styles/Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const location = useLocation();\n  const navItems = [{\n    id: '/',\n    label: 'Home'\n  }, {\n    id: '/about',\n    label: 'About'\n  }, {\n    id: '/research',\n    label: 'Research'\n  }, {\n    id: '/publications',\n    label: 'Publications'\n  }, {\n    id: '/teaching',\n    label: 'Teaching'\n  }, {\n    id: '/contact',\n    label: 'Contact'\n  }];\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const handleNavClick = () => {\n    setIsMobileMenuOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(motion.header, {\n    className: `header ${isScrolled ? 'scrolled' : ''}`,\n    initial: {\n      y: -100\n    },\n    animate: {\n      y: 0\n    },\n    transition: {\n      duration: 0.6,\n      ease: \"easeOut\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"logo\",\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-circle\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Prof\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logo-text\",\n              children: \"Ms. Hymavathi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"nav-desktop\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"nav-list\",\n            children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: item.id,\n                className: `nav-link ${location.pathname === item.id ? 'active' : ''}`,\n                onClick: handleNavClick,\n                children: [item.label, location.pathname === item.id && /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"nav-indicator\",\n                  layoutId: \"nav-indicator\",\n                  transition: {\n                    type: \"spring\",\n                    stiffness: 300,\n                    damping: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this)\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"mobile-menu-toggle\",\n          onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n          \"aria-label\": \"Toggle mobile menu\",\n          children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 33\n          }, this) : /*#__PURE__*/_jsxDEV(FaBars, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: `mobile-menu ${isMobileMenuOpen ? 'open' : ''}`,\n      initial: {\n        opacity: 0,\n        height: 0\n      },\n      animate: {\n        opacity: isMobileMenuOpen ? 1 : 0,\n        height: isMobileMenuOpen ? 'auto' : 0\n      },\n      transition: {\n        duration: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"mobile-nav\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"mobile-nav-list\",\n          children: navItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.li, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: isMobileMenuOpen ? 1 : 0,\n              x: isMobileMenuOpen ? 0 : -20\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: item.id,\n              className: `mobile-nav-link ${location.pathname === item.id ? 'active' : ''}`,\n              onClick: handleNavClick,\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this)\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"ZZC0BIEWBy+IYRqzkNBQSX1kkcg=\", false, function () {\n  return [useLocation];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Link", "useLocation", "FaBars", "FaTimes", "jsxDEV", "_jsxDEV", "Header", "_s", "isScrolled", "setIsScrolled", "isMobileMenuOpen", "setIsMobileMenuOpen", "location", "navItems", "id", "label", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "handleNavClick", "header", "className", "initial", "y", "animate", "transition", "duration", "ease", "children", "to", "div", "whileHover", "scale", "whileTap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "pathname", "onClick", "layoutId", "type", "stiffness", "damping", "opacity", "height", "index", "li", "x", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/components/Header.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useLocation } from 'react-router-dom';\nimport { FaBars, FaTimes } from 'react-icons/fa';\nimport '../styles/Header.css';\n\nconst Header = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const location = useLocation();\n\n  const navItems = [\n    { id: '/', label: 'Home' },\n    { id: '/about', label: 'About' },\n    { id: '/research', label: 'Research' },\n    { id: '/publications', label: 'Publications' },\n    { id: '/teaching', label: 'Teaching' },\n    { id: '/contact', label: 'Contact' }\n  ];\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleNavClick = () => {\n    setIsMobileMenuOpen(false);\n  };\n\n  return (\n    <motion.header\n      className={`header ${isScrolled ? 'scrolled' : ''}`}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: \"easeOut\" }}\n    >\n      <div className=\"container\">\n        <div className=\"header-content\">\n          <Link to=\"/\">\n            <motion.div\n              className=\"logo\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <div className=\"logo-circle\">\n                <span>Prof</span>\n              </div>\n              <span className=\"logo-text\">Ms. Hymavathi</span>\n            </motion.div>\n          </Link>\n\n          <nav className=\"nav-desktop\">\n            <ul className=\"nav-list\">\n              {navItems.map((item) => (\n                <li key={item.id}>\n                  <Link\n                    to={item.id}\n                    className={`nav-link ${location.pathname === item.id ? 'active' : ''}`}\n                    onClick={handleNavClick}\n                  >\n                    {item.label}\n                    {location.pathname === item.id && (\n                      <motion.div\n                        className=\"nav-indicator\"\n                        layoutId=\"nav-indicator\"\n                        transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                      />\n                    )}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </nav>\n\n          <button\n            className=\"mobile-menu-toggle\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            aria-label=\"Toggle mobile menu\"\n          >\n            {isMobileMenuOpen ? <FaTimes /> : <FaBars />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <motion.div\n        className={`mobile-menu ${isMobileMenuOpen ? 'open' : ''}`}\n        initial={{ opacity: 0, height: 0 }}\n        animate={{\n          opacity: isMobileMenuOpen ? 1 : 0,\n          height: isMobileMenuOpen ? 'auto' : 0\n        }}\n        transition={{ duration: 0.3 }}\n      >\n        <nav className=\"mobile-nav\">\n          <ul className=\"mobile-nav-list\">\n            {navItems.map((item, index) => (\n              <motion.li\n                key={item.id}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{\n                  opacity: isMobileMenuOpen ? 1 : 0,\n                  x: isMobileMenuOpen ? 0 : -20\n                }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <Link\n                  to={item.id}\n                  className={`mobile-nav-link ${location.pathname === item.id ? 'active' : ''}`}\n                  onClick={handleNavClick}\n                >\n                  {item.label}\n                </Link>\n              </motion.li>\n            ))}\n          </ul>\n        </nav>\n      </motion.div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAChD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMe,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,QAAQ,GAAG,CACf;IAAEC,EAAE,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC1B;IAAED,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAChC;IAAED,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAC,EACtC;IAAED,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC9C;IAAED,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAC,EACtC;IAAED,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAC,CACrC;EAEDjB,SAAS,CAAC,MAAM;IACd,MAAMkB,YAAY,GAAGA,CAAA,KAAM;MACzBP,aAAa,CAACQ,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,cAAc,GAAGA,CAAA,KAAM;IAC3BV,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,oBACEN,OAAA,CAACN,MAAM,CAACuB,MAAM;IACZC,SAAS,EAAE,UAAUf,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;IACpDgB,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,UAAU,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAU,CAAE;IAAAC,QAAA,gBAE/CzB,OAAA;MAAKkB,SAAS,EAAC,WAAW;MAAAO,QAAA,eACxBzB,OAAA;QAAKkB,SAAS,EAAC,gBAAgB;QAAAO,QAAA,gBAC7BzB,OAAA,CAACL,IAAI;UAAC+B,EAAE,EAAC,GAAG;UAAAD,QAAA,eACVzB,OAAA,CAACN,MAAM,CAACiC,GAAG;YACTT,SAAS,EAAC,MAAM;YAChBU,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAJ,QAAA,gBAE1BzB,OAAA;cAAKkB,SAAS,EAAC,aAAa;cAAAO,QAAA,eAC1BzB,OAAA;gBAAAyB,QAAA,EAAM;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACNlC,OAAA;cAAMkB,SAAS,EAAC,WAAW;cAAAO,QAAA,EAAC;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEPlC,OAAA;UAAKkB,SAAS,EAAC,aAAa;UAAAO,QAAA,eAC1BzB,OAAA;YAAIkB,SAAS,EAAC,UAAU;YAAAO,QAAA,EACrBjB,QAAQ,CAAC2B,GAAG,CAAEC,IAAI,iBACjBpC,OAAA;cAAAyB,QAAA,eACEzB,OAAA,CAACL,IAAI;gBACH+B,EAAE,EAAEU,IAAI,CAAC3B,EAAG;gBACZS,SAAS,EAAE,YAAYX,QAAQ,CAAC8B,QAAQ,KAAKD,IAAI,CAAC3B,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACvE6B,OAAO,EAAEtB,cAAe;gBAAAS,QAAA,GAEvBW,IAAI,CAAC1B,KAAK,EACVH,QAAQ,CAAC8B,QAAQ,KAAKD,IAAI,CAAC3B,EAAE,iBAC5BT,OAAA,CAACN,MAAM,CAACiC,GAAG;kBACTT,SAAS,EAAC,eAAe;kBACzBqB,QAAQ,EAAC,eAAe;kBACxBjB,UAAU,EAAE;oBAAEkB,IAAI,EAAE,QAAQ;oBAAEC,SAAS,EAAE,GAAG;oBAAEC,OAAO,EAAE;kBAAG;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GAdAE,IAAI,CAAC3B,EAAE;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENlC,OAAA;UACEkB,SAAS,EAAC,oBAAoB;UAC9BoB,OAAO,EAAEA,CAAA,KAAMhC,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;UACtD,cAAW,oBAAoB;UAAAoB,QAAA,EAE9BpB,gBAAgB,gBAAGL,OAAA,CAACF,OAAO;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGlC,OAAA,CAACH,MAAM;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA,CAACN,MAAM,CAACiC,GAAG;MACTT,SAAS,EAAE,eAAeb,gBAAgB,GAAG,MAAM,GAAG,EAAE,EAAG;MAC3Dc,OAAO,EAAE;QAAEwB,OAAO,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAE;MACnCvB,OAAO,EAAE;QACPsB,OAAO,EAAEtC,gBAAgB,GAAG,CAAC,GAAG,CAAC;QACjCuC,MAAM,EAAEvC,gBAAgB,GAAG,MAAM,GAAG;MACtC,CAAE;MACFiB,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAE,QAAA,eAE9BzB,OAAA;QAAKkB,SAAS,EAAC,YAAY;QAAAO,QAAA,eACzBzB,OAAA;UAAIkB,SAAS,EAAC,iBAAiB;UAAAO,QAAA,EAC5BjB,QAAQ,CAAC2B,GAAG,CAAC,CAACC,IAAI,EAAES,KAAK,kBACxB7C,OAAA,CAACN,MAAM,CAACoD,EAAE;YAER3B,OAAO,EAAE;cAAEwB,OAAO,EAAE,CAAC;cAAEI,CAAC,EAAE,CAAC;YAAG,CAAE;YAChC1B,OAAO,EAAE;cACPsB,OAAO,EAAEtC,gBAAgB,GAAG,CAAC,GAAG,CAAC;cACjC0C,CAAC,EAAE1C,gBAAgB,GAAG,CAAC,GAAG,CAAC;YAC7B,CAAE;YACFiB,UAAU,EAAE;cAAE0B,KAAK,EAAEH,KAAK,GAAG;YAAI,CAAE;YAAApB,QAAA,eAEnCzB,OAAA,CAACL,IAAI;cACH+B,EAAE,EAAEU,IAAI,CAAC3B,EAAG;cACZS,SAAS,EAAE,mBAAmBX,QAAQ,CAAC8B,QAAQ,KAAKD,IAAI,CAAC3B,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC9E6B,OAAO,EAAEtB,cAAe;cAAAS,QAAA,EAEvBW,IAAI,CAAC1B;YAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC,GAdFE,IAAI,CAAC3B,EAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeH,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEpB,CAAC;AAAChC,EAAA,CAtHID,MAAM;EAAA,QAGOL,WAAW;AAAA;AAAAqD,EAAA,GAHxBhD,MAAM;AAwHZ,eAAeA,MAAM;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}