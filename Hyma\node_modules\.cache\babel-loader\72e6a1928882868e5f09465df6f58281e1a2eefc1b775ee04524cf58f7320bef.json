{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\pages\\\\Teaching.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaGraduationCap, FaChalkboardTeacher, FaStar, FaUsers, FaBook, FaArrowLeft } from 'react-icons/fa';\nimport '../styles/Teaching.css';\nimport '../styles/PageLayout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Teaching = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('courses');\n  const courses = [{\n    code: \"CERT 01\",\n    title: \"What is Data Science?\",\n    level: \"Coursera\",\n    students: \"Self-paced\",\n    rating: 5.0,\n    description: \"Comprehensive course on data science fundamentals and applications.\",\n    topics: [\"Data Analysis\", \"Statistical Methods\", \"Data Visualization\", \"Python Programming\"]\n  }, {\n    code: \"CERT 02\",\n    title: \".Net Full Stack Development\",\n    level: \"Wipro\",\n    students: \"Professional\",\n    rating: 5.0,\n    description: \"Complete full-stack development using .NET technologies.\",\n    topics: [\"ASP.NET\", \"C#\", \"SQL Server\", \"Web Development\", \"API Development\"]\n  }, {\n    code: \"CERT 03\",\n    title: \"Project Management Specialization\",\n    level: \"Coursera\",\n    students: \"Professional\",\n    rating: 5.0,\n    description: \"Comprehensive project management training covering all phases.\",\n    topics: [\"Project Planning\", \"Execution\", \"Risk Management\", \"Team Leadership\"]\n  }, {\n    code: \"CERT 04\",\n    title: \"Programming Fundamentals using Python\",\n    level: \"Infosys\",\n    students: \"Professional\",\n    rating: 5.0,\n    description: \"Foundation course in Python programming and software development.\",\n    topics: [\"Python Basics\", \"Data Structures\", \"Algorithms\", \"Object-Oriented Programming\"]\n  }];\n  const achievements = [{\n    icon: /*#__PURE__*/_jsxDEV(FaGraduationCap, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this),\n    title: \"Women Entrepreneurship Development Program\",\n    year: \"2023\",\n    description: \"Three-week program by Department of Science and Technology, Govt. of India\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaStar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    title: \"Cloud Infrastructure (AWS) FDP\",\n    year: \"2023\",\n    description: \"One-week National FDP by Brainovision Solutions in collaboration with AICTE\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this),\n    title: \"AI and Cyber Security FDP\",\n    year: \"2022\",\n    description: \"One-week FDP on Recent Trends in AI and Cyber Security\"\n  }];\n  const testimonials = [{\n    name: \"Sarah Johnson\",\n    course: \"CS 601 - Advanced Machine Learning\",\n    text: \"Dr. Academic's teaching style is exceptional. Complex concepts become clear through practical examples and hands-on projects.\",\n    rating: 5\n  }, {\n    name: \"Michael Chen\",\n    course: \"CS 401 - Introduction to AI\",\n    text: \"The best professor I've had. Always available for questions and genuinely cares about student success.\",\n    rating: 5\n  }, {\n    name: \"Emily Rodriguez\",\n    course: \"CS 501 - Computer Vision\",\n    text: \"Challenging course but incredibly rewarding. The projects were directly applicable to real-world problems.\",\n    rating: 5\n  }];\n  const renderStars = rating => {\n    return Array.from({\n      length: 5\n    }, (_, i) => /*#__PURE__*/_jsxDEV(FaStar, {\n      className: `star ${i < rating ? 'filled' : ''}`\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"page-header\",\n      initial: {\n        opacity: 0,\n        y: -30\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"back-link\",\n          children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: \"Teaching & Mentorship\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Discover my courses, teaching philosophy, and achievements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section teaching\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"teaching-philosophy\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"philosophy-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaChalkboardTeacher, {\n              className: \"philosophy-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"philosophy-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Teaching Philosophy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"I believe in creating an inclusive, engaging learning environment where students are encouraged to think critically and explore innovative solutions. My approach combines theoretical foundations with practical applications, ensuring students gain both deep understanding and real-world skills.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"teaching-tabs\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.4\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-btn ${activeTab === 'courses' ? 'active' : ''}`,\n            onClick: () => setActiveTab('courses'),\n            children: [/*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), \"Certifications\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-btn ${activeTab === 'achievements' ? 'active' : ''}`,\n            onClick: () => setActiveTab('achievements'),\n            children: [/*#__PURE__*/_jsxDEV(FaStar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), \"Workshops\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-btn ${activeTab === 'testimonials' ? 'active' : ''}`,\n            onClick: () => setActiveTab('testimonials'),\n            children: [/*#__PURE__*/_jsxDEV(FaUsers, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), \"Testimonials\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: [activeTab === 'courses' && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"courses-grid\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: courses.map((course, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"course-card\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              whileHover: {\n                y: -5\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"course-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"course-code\",\n                  children: course.code\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"course-level\",\n                  children: course.level\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"course-title\",\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"course-description\",\n                children: course.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"course-topics\",\n                children: course.topics.map(topic => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"topic-tag\",\n                  children: topic\n                }, topic, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"course-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stat\",\n                  children: [/*#__PURE__*/_jsxDEV(FaUsers, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [course.students, \" students\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stat\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"rating\",\n                    children: [renderStars(Math.floor(course.rating)), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"rating-number\",\n                      children: course.rating\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 21\n              }, this)]\n            }, course.code, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), activeTab === 'achievements' && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"achievements-grid\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: achievements.map((achievement, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"achievement-card\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              whileHover: {\n                scale: 1.02\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"achievement-icon\",\n                children: achievement.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"achievement-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: achievement.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"achievement-year\",\n                  children: achievement.year\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: achievement.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 21\n              }, this)]\n            }, achievement.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), activeTab === 'testimonials' && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"testimonials-grid\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: testimonials.map((testimonial, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"testimonial-card\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              whileHover: {\n                y: -5\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"testimonial-rating\",\n                children: renderStars(testimonial.rating)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"testimonial-text\",\n                children: [\"\\\"\", testimonial.text, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"testimonial-author\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: testimonial.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"testimonial-course\",\n                  children: testimonial.course\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 21\n              }, this)]\n            }, testimonial.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(Teaching, \"KZyh1LmFN2QoWawvH9dKiAzLmZM=\");\n_c = Teaching;\nexport default Teaching;\nvar _c;\n$RefreshReg$(_c, \"Teaching\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "FaGraduationCap", "FaChalkboardTeacher", "FaStar", "FaUsers", "FaBook", "FaArrowLeft", "jsxDEV", "_jsxDEV", "Teaching", "_s", "activeTab", "setActiveTab", "courses", "code", "title", "level", "students", "rating", "description", "topics", "achievements", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "year", "testimonials", "name", "course", "text", "renderStars", "Array", "from", "length", "_", "i", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "to", "delay", "onClick", "map", "index", "whileHover", "topic", "Math", "floor", "achievement", "scale", "testimonial", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/pages/Teaching.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaGraduationCap, FaChalkboardTeacher, FaStar, FaUsers, FaBook, FaArrowLeft } from 'react-icons/fa';\nimport '../styles/Teaching.css';\nimport '../styles/PageLayout.css';\n\nconst Teaching = () => {\n  const [activeTab, setActiveTab] = useState('courses');\n\n  const courses = [\n    {\n      code: \"CERT 01\",\n      title: \"What is Data Science?\",\n      level: \"Coursera\",\n      students: \"Self-paced\",\n      rating: 5.0,\n      description: \"Comprehensive course on data science fundamentals and applications.\",\n      topics: [\"Data Analysis\", \"Statistical Methods\", \"Data Visualization\", \"Python Programming\"]\n    },\n    {\n      code: \"CERT 02\",\n      title: \".Net Full Stack Development\",\n      level: \"Wipro\",\n      students: \"Professional\",\n      rating: 5.0,\n      description: \"Complete full-stack development using .NET technologies.\",\n      topics: [\"ASP.NET\", \"C#\", \"SQL Server\", \"Web Development\", \"API Development\"]\n    },\n    {\n      code: \"CERT 03\",\n      title: \"Project Management Specialization\",\n      level: \"Coursera\",\n      students: \"Professional\",\n      rating: 5.0,\n      description: \"Comprehensive project management training covering all phases.\",\n      topics: [\"Project Planning\", \"Execution\", \"Risk Management\", \"Team Leadership\"]\n    },\n    {\n      code: \"CERT 04\",\n      title: \"Programming Fundamentals using Python\",\n      level: \"Infosys\",\n      students: \"Professional\",\n      rating: 5.0,\n      description: \"Foundation course in Python programming and software development.\",\n      topics: [\"Python Basics\", \"Data Structures\", \"Algorithms\", \"Object-Oriented Programming\"]\n    }\n  ];\n\n  const achievements = [\n    {\n      icon: <FaGraduationCap />,\n      title: \"Women Entrepreneurship Development Program\",\n      year: \"2023\",\n      description: \"Three-week program by Department of Science and Technology, Govt. of India\"\n    },\n    {\n      icon: <FaStar />,\n      title: \"Cloud Infrastructure (AWS) FDP\",\n      year: \"2023\",\n      description: \"One-week National FDP by Brainovision Solutions in collaboration with AICTE\"\n    },\n    {\n      icon: <FaBook />,\n      title: \"AI and Cyber Security FDP\",\n      year: \"2022\",\n      description: \"One-week FDP on Recent Trends in AI and Cyber Security\"\n    }\n  ];\n\n  const testimonials = [\n    {\n      name: \"Sarah Johnson\",\n      course: \"CS 601 - Advanced Machine Learning\",\n      text: \"Dr. Academic's teaching style is exceptional. Complex concepts become clear through practical examples and hands-on projects.\",\n      rating: 5\n    },\n    {\n      name: \"Michael Chen\",\n      course: \"CS 401 - Introduction to AI\",\n      text: \"The best professor I've had. Always available for questions and genuinely cares about student success.\",\n      rating: 5\n    },\n    {\n      name: \"Emily Rodriguez\",\n      course: \"CS 501 - Computer Vision\",\n      text: \"Challenging course but incredibly rewarding. The projects were directly applicable to real-world problems.\",\n      rating: 5\n    }\n  ];\n\n  const renderStars = (rating) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <FaStar\n        key={i}\n        className={`star ${i < rating ? 'filled' : ''}`}\n      />\n    ));\n  };\n\n  return (\n    <div className=\"page-container\">\n      {/* Page Header */}\n      <motion.div\n        className=\"page-header\"\n        initial={{ opacity: 0, y: -30 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <div className=\"container\">\n          <Link to=\"/\" className=\"back-link\">\n            <FaArrowLeft />\n            Back to Home\n          </Link>\n          <h1 className=\"page-title\">Teaching & Mentorship</h1>\n          <p className=\"page-subtitle\">Discover my courses, teaching philosophy, and achievements</p>\n        </div>\n      </motion.div>\n\n      <section className=\"section teaching\">\n        <div className=\"container\">\n          {/* Teaching Philosophy */}\n          <motion.div\n            className=\"teaching-philosophy\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            <div className=\"philosophy-content\">\n              <FaChalkboardTeacher className=\"philosophy-icon\" />\n              <div className=\"philosophy-text\">\n                <h3>Teaching Philosophy</h3>\n                <p>\n                  I believe in creating an inclusive, engaging learning environment where students \n                  are encouraged to think critically and explore innovative solutions. My approach \n                  combines theoretical foundations with practical applications, ensuring students \n                  gain both deep understanding and real-world skills.\n                </p>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Tab Navigation */}\n          <motion.div\n            className=\"teaching-tabs\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            <button\n              className={`tab-btn ${activeTab === 'courses' ? 'active' : ''}`}\n              onClick={() => setActiveTab('courses')}\n            >\n              <FaBook />\n              Certifications\n            </button>\n            <button\n              className={`tab-btn ${activeTab === 'achievements' ? 'active' : ''}`}\n              onClick={() => setActiveTab('achievements')}\n            >\n              <FaStar />\n              Workshops\n            </button>\n            <button\n              className={`tab-btn ${activeTab === 'testimonials' ? 'active' : ''}`}\n              onClick={() => setActiveTab('testimonials')}\n            >\n              <FaUsers />\n              Testimonials\n            </button>\n          </motion.div>\n\n          {/* Tab Content */}\n          <div className=\"tab-content\">\n            {activeTab === 'courses' && (\n              <motion.div\n                className=\"courses-grid\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n              >\n                {courses.map((course, index) => (\n                  <motion.div\n                    key={course.code}\n                    className=\"course-card\"\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\n                    whileHover={{ y: -5 }}\n                  >\n                    <div className=\"course-header\">\n                      <div className=\"course-code\">{course.code}</div>\n                      <div className=\"course-level\">{course.level}</div>\n                    </div>\n                    \n                    <h4 className=\"course-title\">{course.title}</h4>\n                    <p className=\"course-description\">{course.description}</p>\n                    \n                    <div className=\"course-topics\">\n                      {course.topics.map(topic => (\n                        <span key={topic} className=\"topic-tag\">{topic}</span>\n                      ))}\n                    </div>\n                    \n                    <div className=\"course-stats\">\n                      <div className=\"stat\">\n                        <FaUsers />\n                        <span>{course.students} students</span>\n                      </div>\n                      <div className=\"stat\">\n                        <div className=\"rating\">\n                          {renderStars(Math.floor(course.rating))}\n                          <span className=\"rating-number\">{course.rating}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n\n            {activeTab === 'achievements' && (\n              <motion.div\n                className=\"achievements-grid\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n              >\n                {achievements.map((achievement, index) => (\n                  <motion.div\n                    key={achievement.title}\n                    className=\"achievement-card\"\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\n                    whileHover={{ scale: 1.02 }}\n                  >\n                    <div className=\"achievement-icon\">{achievement.icon}</div>\n                    <div className=\"achievement-content\">\n                      <h4>{achievement.title}</h4>\n                      <div className=\"achievement-year\">{achievement.year}</div>\n                      <p>{achievement.description}</p>\n                    </div>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n\n            {activeTab === 'testimonials' && (\n              <motion.div\n                className=\"testimonials-grid\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n              >\n                {testimonials.map((testimonial, index) => (\n                  <motion.div\n                    key={testimonial.name}\n                    className=\"testimonial-card\"\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\n                    whileHover={{ y: -5 }}\n                  >\n                    <div className=\"testimonial-rating\">\n                      {renderStars(testimonial.rating)}\n                    </div>\n                    <p className=\"testimonial-text\">\"{testimonial.text}\"</p>\n                    <div className=\"testimonial-author\">\n                      <strong>{testimonial.name}</strong>\n                      <span className=\"testimonial-course\">{testimonial.course}</span>\n                    </div>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Teaching;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,eAAe,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,WAAW,QAAQ,gBAAgB;AAC3G,OAAO,wBAAwB;AAC/B,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,SAAS,CAAC;EAErD,MAAMe,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,qEAAqE;IAClFC,MAAM,EAAE,CAAC,eAAe,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,6BAA6B;IACpCC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,0DAA0D;IACvEC,MAAM,EAAE,CAAC,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB;EAC9E,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,mCAAmC;IAC1CC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,gEAAgE;IAC7EC,MAAM,EAAE,CAAC,kBAAkB,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB;EAChF,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,uCAAuC;IAC9CC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,mEAAmE;IAChFC,MAAM,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,YAAY,EAAE,6BAA6B;EAC1F,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,CACnB;IACEC,IAAI,eAAEd,OAAA,CAACP,eAAe;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBX,KAAK,EAAE,4CAA4C;IACnDY,IAAI,EAAE,MAAM;IACZR,WAAW,EAAE;EACf,CAAC,EACD;IACEG,IAAI,eAAEd,OAAA,CAACL,MAAM;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBX,KAAK,EAAE,gCAAgC;IACvCY,IAAI,EAAE,MAAM;IACZR,WAAW,EAAE;EACf,CAAC,EACD;IACEG,IAAI,eAAEd,OAAA,CAACH,MAAM;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBX,KAAK,EAAE,2BAA2B;IAClCY,IAAI,EAAE,MAAM;IACZR,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMS,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,eAAe;IACrBC,MAAM,EAAE,oCAAoC;IAC5CC,IAAI,EAAE,+HAA+H;IACrIb,MAAM,EAAE;EACV,CAAC,EACD;IACEW,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,6BAA6B;IACrCC,IAAI,EAAE,wGAAwG;IAC9Gb,MAAM,EAAE;EACV,CAAC,EACD;IACEW,IAAI,EAAE,iBAAiB;IACvBC,MAAM,EAAE,0BAA0B;IAClCC,IAAI,EAAE,4GAA4G;IAClHb,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMc,WAAW,GAAId,MAAM,IAAK;IAC9B,OAAOe,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,kBACpC7B,OAAA,CAACL,MAAM;MAELmC,SAAS,EAAE,QAAQD,CAAC,GAAGnB,MAAM,GAAG,QAAQ,GAAG,EAAE;IAAG,GAD3CmB,CAAC;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP,CACF,CAAC;EACJ,CAAC;EAED,oBACElB,OAAA;IAAK8B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7B/B,OAAA,CAACT,MAAM,CAACyC,GAAG;MACTF,SAAS,EAAC,aAAa;MACvBG,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAP,QAAA,eAE9B/B,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/B,OAAA,CAACR,IAAI;UAAC+C,EAAE,EAAC,GAAG;UAACT,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAChC/B,OAAA,CAACF,WAAW;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPlB,OAAA;UAAI8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAqB;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrDlB,OAAA;UAAG8B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0D;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEblB,OAAA;MAAS8B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnC/B,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAExB/B,OAAA,CAACT,MAAM,CAACyC,GAAG;UACTF,SAAS,EAAC,qBAAqB;UAC/BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAAAT,QAAA,eAE1C/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC/B,OAAA,CAACN,mBAAmB;cAACoC,SAAS,EAAC;YAAiB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDlB,OAAA;cAAK8B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B/B,OAAA;gBAAA+B,QAAA,EAAI;cAAmB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BlB,OAAA;gBAAA+B,QAAA,EAAG;cAKH;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGblB,OAAA,CAACT,MAAM,CAACyC,GAAG;UACTF,SAAS,EAAC,eAAe;UACzBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAAAT,QAAA,gBAE1C/B,OAAA;YACE8B,SAAS,EAAE,WAAW3B,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YAChEsC,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAAC,SAAS,CAAE;YAAA2B,QAAA,gBAEvC/B,OAAA,CAACH,MAAM;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAEZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlB,OAAA;YACE8B,SAAS,EAAE,WAAW3B,SAAS,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAE,EAAG;YACrEsC,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAAC,cAAc,CAAE;YAAA2B,QAAA,gBAE5C/B,OAAA,CAACL,MAAM;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAEZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlB,OAAA;YACE8B,SAAS,EAAE,WAAW3B,SAAS,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAE,EAAG;YACrEsC,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAAC,cAAc,CAAE;YAAA2B,QAAA,gBAE5C/B,OAAA,CAACJ,OAAO;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGblB,OAAA;UAAK8B,SAAS,EAAC,aAAa;UAAAC,QAAA,GACzB5B,SAAS,KAAK,SAAS,iBACtBH,OAAA,CAACT,MAAM,CAACyC,GAAG;YACTF,SAAS,EAAC,cAAc;YACxBG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,EAE7B1B,OAAO,CAACqC,GAAG,CAAC,CAACpB,MAAM,EAAEqB,KAAK,kBACzB3C,OAAA,CAACT,MAAM,CAACyC,GAAG;cAETF,SAAS,EAAC,aAAa;cACvBG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEE,KAAK,EAAEG,KAAK,GAAG;cAAI,CAAE;cAClDC,UAAU,EAAE;gBAAET,CAAC,EAAE,CAAC;cAAE,CAAE;cAAAJ,QAAA,gBAEtB/B,OAAA;gBAAK8B,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B/B,OAAA;kBAAK8B,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAET,MAAM,CAAChB;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDlB,OAAA;kBAAK8B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAET,MAAM,CAACd;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eAENlB,OAAA;gBAAI8B,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAET,MAAM,CAACf;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChDlB,OAAA;gBAAG8B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAET,MAAM,CAACX;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE1DlB,OAAA;gBAAK8B,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BT,MAAM,CAACV,MAAM,CAAC8B,GAAG,CAACG,KAAK,iBACtB7C,OAAA;kBAAkB8B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEc;gBAAK,GAAnCA,KAAK;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlB,OAAA;gBAAK8B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B/B,OAAA;kBAAK8B,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB/B,OAAA,CAACJ,OAAO;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACXlB,OAAA;oBAAA+B,QAAA,GAAOT,MAAM,CAACb,QAAQ,EAAC,WAAS;kBAAA;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACNlB,OAAA;kBAAK8B,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB/B,OAAA;oBAAK8B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,GACpBP,WAAW,CAACsB,IAAI,CAACC,KAAK,CAACzB,MAAM,CAACZ,MAAM,CAAC,CAAC,eACvCV,OAAA;sBAAM8B,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAET,MAAM,CAACZ;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAhCDI,MAAM,CAAChB,IAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiCN,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CACb,EAEAf,SAAS,KAAK,cAAc,iBAC3BH,OAAA,CAACT,MAAM,CAACyC,GAAG;YACTF,SAAS,EAAC,mBAAmB;YAC7BG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,EAE7BlB,YAAY,CAAC6B,GAAG,CAAC,CAACM,WAAW,EAAEL,KAAK,kBACnC3C,OAAA,CAACT,MAAM,CAACyC,GAAG;cAETF,SAAS,EAAC,kBAAkB;cAC5BG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEE,KAAK,EAAEG,KAAK,GAAG;cAAI,CAAE;cAClDC,UAAU,EAAE;gBAAEK,KAAK,EAAE;cAAK,CAAE;cAAAlB,QAAA,gBAE5B/B,OAAA;gBAAK8B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEiB,WAAW,CAAClC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DlB,OAAA;gBAAK8B,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC/B,OAAA;kBAAA+B,QAAA,EAAKiB,WAAW,CAACzC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5BlB,OAAA;kBAAK8B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEiB,WAAW,CAAC7B;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DlB,OAAA;kBAAA+B,QAAA,EAAIiB,WAAW,CAACrC;gBAAW;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA,GAZD8B,WAAW,CAACzC,KAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaZ,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CACb,EAEAf,SAAS,KAAK,cAAc,iBAC3BH,OAAA,CAACT,MAAM,CAACyC,GAAG;YACTF,SAAS,EAAC,mBAAmB;YAC7BG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,EAE7BX,YAAY,CAACsB,GAAG,CAAC,CAACQ,WAAW,EAAEP,KAAK,kBACnC3C,OAAA,CAACT,MAAM,CAACyC,GAAG;cAETF,SAAS,EAAC,kBAAkB;cAC5BG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEE,KAAK,EAAEG,KAAK,GAAG;cAAI,CAAE;cAClDC,UAAU,EAAE;gBAAET,CAAC,EAAE,CAAC;cAAE,CAAE;cAAAJ,QAAA,gBAEtB/B,OAAA;gBAAK8B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAChCP,WAAW,CAAC0B,WAAW,CAACxC,MAAM;cAAC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACNlB,OAAA;gBAAG8B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAC,IAAC,EAACmB,WAAW,CAAC3B,IAAI,EAAC,IAAC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxDlB,OAAA;gBAAK8B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC/B,OAAA;kBAAA+B,QAAA,EAASmB,WAAW,CAAC7B;gBAAI;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACnClB,OAAA;kBAAM8B,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAEmB,WAAW,CAAC5B;gBAAM;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAdDgC,WAAW,CAAC7B,IAAI;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeX,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChB,EAAA,CAlRID,QAAQ;AAAAkD,EAAA,GAARlD,QAAQ;AAoRd,eAAeA,QAAQ;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}