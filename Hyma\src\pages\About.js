import React from 'react';
import { motion } from 'framer-motion';
import { FaGraduationCap, FaAward, FaUsers, FaBookOpen, FaArrowLeft } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import '../styles/About.css';
import '../styles/PageLayout.css';

const About = () => {
  const stats = [
    {
      icon: <FaGraduationCap />,
      number: "PhD",
      label: "Pursuing"
    },
    {
      icon: <FaBookOpen />,
      number: "4",
      label: "Publications"
    },
    {
      icon: <FaAward />,
      number: "1",
      label: "Patent"
    },
    {
      icon: <FaUsers />,
      number: "10+",
      label: "Certifications"
    }
  ];

  const expertise = [
    "Artificial Intelligence",
    "Machine Learning",
    "Data Science",
    "Breast Cancer Gene Prediction",
    "Network Security",
    "Differential Evolution Models",
    "DELSTM Models",
    "Project Management"
  ];

  return (
    <div className="page-container">
      {/* Page Header */}
      <motion.div
        className="page-header"
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container">
          <Link to="/" className="back-link">
            <FaArrowLeft />
            Back to Home
          </Link>
          <h1 className="page-title">About Me</h1>
          <p className="page-subtitle">Assistant Professor & PhD Scholar specializing in AI and Machine Learning</p>
        </div>
      </motion.div>

      <section className="section about">
        <div className="container">
          <div className="about-content">
            <motion.div
              className="about-text"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="about-intro">
                <h3>Passionate Educator & Researcher</h3>
                <p>
                  I am an Assistant Professor and PhD Scholar specializing in Computer Science with a focus on
                  artificial intelligence, machine learning, and data science. My research primarily involves
                  breast cancer gene prediction using advanced ML models and network security applications.
                </p>
                <p>
                  I hold an MCA degree and am currently pursuing my PhD at Acharya Nagarjuna University.
                  I am passionate about applying cutting-edge technology to solve real-world problems,
                  particularly in healthcare and cybersecurity domains.
                </p>
              </div>

              <div className="expertise-section">
                <h4>Areas of Expertise</h4>
                <div className="expertise-grid">
                  {expertise.map((skill, index) => (
                    <motion.div
                      key={skill}
                      className="expertise-item"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                    >
                      {skill}
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>

            <motion.div
              className="about-image"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="profile-image-about">
                <img
                  src="/images/hymavathi.jpg"
                  alt="Ms. Hymavathi Thottathyl"
                  className="about-photo"
                />
              </div>
            </motion.div>
          </div>

          <motion.div
            className="stats-section"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <div className="stats-grid">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  className="stat-card"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <div className="stat-icon">{stat.icon}</div>
                  <div className="stat-number">{stat.number}</div>
                  <div className="stat-label">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default About;
