{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\components\\\\Contact.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaEnvelope, FaPhone, FaMapMarkerAlt, FaLinkedin, FaTwitter, FaGoogleScholar, FaOrcid, FaPaperPlane } from 'react-icons/fa';\nimport '../styles/Contact.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Contact = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState('');\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    // Simulate form submission\n    setTimeout(() => {\n      setIsSubmitting(false);\n      setSubmitStatus('success');\n      setFormData({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n      });\n      setTimeout(() => {\n        setSubmitStatus('');\n      }, 5000);\n    }, 2000);\n  };\n  const contactInfo = [{\n    icon: /*#__PURE__*/_jsxDEV(FaEnvelope, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this),\n    title: \"Email\",\n    value: \"<EMAIL>\",\n    link: \"mailto:<EMAIL>\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaPhone, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this),\n    title: \"Phone\",\n    value: \"+****************\",\n    link: \"tel:+15551234567\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this),\n    title: \"Office\",\n    value: \"Computer Science Building, Room 301\\nUniversity Campus, State 12345\",\n    link: null\n  }];\n  const socialLinks = [{\n    icon: /*#__PURE__*/_jsxDEV(FaLinkedin, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    name: \"LinkedIn\",\n    url: \"https://linkedin.com/in/dr-academic\",\n    color: \"#0077b5\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaGoogleScholar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this),\n    name: \"Google Scholar\",\n    url: \"https://scholar.google.com/citations?user=academic\",\n    color: \"#4285f4\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaOrcid, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this),\n    name: \"ORCID\",\n    url: \"https://orcid.org/0000-0000-0000-0000\",\n    color: \"#a6ce39\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaTwitter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this),\n    name: \"Twitter\",\n    url: \"https://twitter.com/dr_academic\",\n    color: \"#1da1f2\"\n  }];\n  const officeHours = [{\n    day: \"Monday\",\n    time: \"2:00 PM - 4:00 PM\"\n  }, {\n    day: \"Wednesday\",\n    time: \"10:00 AM - 12:00 PM\"\n  }, {\n    day: \"Friday\",\n    time: \"1:00 PM - 3:00 PM\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"contact\",\n    className: \"section contact\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n        className: \"section-title\",\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        viewport: {\n          once: true\n        },\n        children: \"Get In Touch\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contact-content\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"contact-info\",\n          initial: {\n            opacity: 0,\n            x: -50\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Contact Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"contact-description\",\n            children: \"I'm always open to discussing research collaborations, academic opportunities, or answering questions about my work. Feel free to reach out!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-details\",\n            children: contactInfo.map((info, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"contact-item\",\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"contact-icon\",\n                children: info.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"contact-text\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: info.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 21\n                }, this), info.link ? /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: info.link,\n                  className: \"contact-link\",\n                  children: info.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: info.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this)]\n            }, info.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"office-hours\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.4\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Office Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hours-list\",\n              children: officeHours.map((hour, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hour-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"day\",\n                  children: hour.day\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"time\",\n                  children: hour.time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this)]\n              }, hour.day, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"social-links\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Connect With Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"social-grid\",\n              children: socialLinks.map((social, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n                href: social.url,\n                className: \"social-link\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                style: {\n                  '--social-color': social.color\n                },\n                whileHover: {\n                  scale: 1.1\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: [social.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: social.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this)]\n              }, social.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"contact-form-container\",\n          initial: {\n            opacity: 0,\n            x: 50\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"contact-form\",\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Send a Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                required: true,\n                placeholder: \"Your full name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                children: \"Email Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                required: true,\n                placeholder: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"subject\",\n                children: \"Subject\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"subject\",\n                name: \"subject\",\n                value: formData.subject,\n                onChange: handleInputChange,\n                required: true,\n                placeholder: \"What is this regarding?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"message\",\n                children: \"Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"message\",\n                name: \"message\",\n                value: formData.message,\n                onChange: handleInputChange,\n                required: true,\n                rows: \"6\",\n                placeholder: \"Your message here...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"submit\",\n              className: \"submit-btn\",\n              disabled: isSubmitting,\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: isSubmitting ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"loading\",\n                children: \"Sending...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(FaPaperPlane, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this), \"Send Message\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), submitStatus === 'success' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"success-message\",\n              initial: {\n                opacity: 0,\n                y: 10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5\n              },\n              children: \"Thank you! Your message has been sent successfully.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(Contact, \"3JlnJRrqjrE5a4o1OG7SNYEveT4=\");\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["React", "useState", "motion", "FaEnvelope", "FaPhone", "FaMapMarkerAlt", "FaLinkedin", "FaTwitter", "FaGoogleScholar", "FaOrcid", "FaPaperPlane", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Contact", "_s", "formData", "setFormData", "name", "email", "subject", "message", "isSubmitting", "setIsSubmitting", "submitStatus", "setSubmitStatus", "handleInputChange", "e", "value", "target", "prev", "handleSubmit", "preventDefault", "setTimeout", "contactInfo", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "link", "socialLinks", "url", "color", "officeHours", "day", "time", "id", "className", "children", "h2", "initial", "opacity", "y", "whileInView", "transition", "duration", "viewport", "once", "div", "x", "map", "info", "index", "delay", "href", "hour", "social", "a", "rel", "style", "whileHover", "scale", "whileTap", "onSubmit", "htmlFor", "type", "onChange", "required", "placeholder", "rows", "button", "disabled", "animate", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/components/Contact.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaEnvelope, FaPhone, FaMapMarkerAlt, FaLinkedin, FaTwitter, FaGoogleScholar, FaOrcid, FaPaperPlane } from 'react-icons/fa';\nimport '../styles/Contact.css';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState('');\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    setTimeout(() => {\n      setIsSubmitting(false);\n      setSubmitStatus('success');\n      setFormData({ name: '', email: '', subject: '', message: '' });\n      \n      setTimeout(() => {\n        setSubmitStatus('');\n      }, 5000);\n    }, 2000);\n  };\n\n  const contactInfo = [\n    {\n      icon: <FaEnvelope />,\n      title: \"Email\",\n      value: \"<EMAIL>\",\n      link: \"mailto:<EMAIL>\"\n    },\n    {\n      icon: <FaPhone />,\n      title: \"Phone\",\n      value: \"+****************\",\n      link: \"tel:+15551234567\"\n    },\n    {\n      icon: <FaMapMarkerAlt />,\n      title: \"Office\",\n      value: \"Computer Science Building, Room 301\\nUniversity Campus, State 12345\",\n      link: null\n    }\n  ];\n\n  const socialLinks = [\n    {\n      icon: <FaLinkedin />,\n      name: \"LinkedIn\",\n      url: \"https://linkedin.com/in/dr-academic\",\n      color: \"#0077b5\"\n    },\n    {\n      icon: <FaGoogleScholar />,\n      name: \"Google Scholar\",\n      url: \"https://scholar.google.com/citations?user=academic\",\n      color: \"#4285f4\"\n    },\n    {\n      icon: <FaOrcid />,\n      name: \"ORCID\",\n      url: \"https://orcid.org/0000-0000-0000-0000\",\n      color: \"#a6ce39\"\n    },\n    {\n      icon: <FaTwitter />,\n      name: \"Twitter\",\n      url: \"https://twitter.com/dr_academic\",\n      color: \"#1da1f2\"\n    }\n  ];\n\n  const officeHours = [\n    { day: \"Monday\", time: \"2:00 PM - 4:00 PM\" },\n    { day: \"Wednesday\", time: \"10:00 AM - 12:00 PM\" },\n    { day: \"Friday\", time: \"1:00 PM - 3:00 PM\" }\n  ];\n\n  return (\n    <section id=\"contact\" className=\"section contact\">\n      <div className=\"container\">\n        <motion.h2\n          className=\"section-title\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n        >\n          Get In Touch\n        </motion.h2>\n\n        <div className=\"contact-content\">\n          {/* Contact Information */}\n          <motion.div\n            className=\"contact-info\"\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h3>Contact Information</h3>\n            <p className=\"contact-description\">\n              I'm always open to discussing research collaborations, academic opportunities, \n              or answering questions about my work. Feel free to reach out!\n            </p>\n\n            <div className=\"contact-details\">\n              {contactInfo.map((info, index) => (\n                <motion.div\n                  key={info.title}\n                  className=\"contact-item\"\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                >\n                  <div className=\"contact-icon\">{info.icon}</div>\n                  <div className=\"contact-text\">\n                    <h4>{info.title}</h4>\n                    {info.link ? (\n                      <a href={info.link} className=\"contact-link\">\n                        {info.value}\n                      </a>\n                    ) : (\n                      <p>{info.value}</p>\n                    )}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Office Hours */}\n            <motion.div\n              className=\"office-hours\"\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              viewport={{ once: true }}\n            >\n              <h4>Office Hours</h4>\n              <div className=\"hours-list\">\n                {officeHours.map((hour, index) => (\n                  <div key={hour.day} className=\"hour-item\">\n                    <span className=\"day\">{hour.day}</span>\n                    <span className=\"time\">{hour.time}</span>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Social Links */}\n            <motion.div\n              className=\"social-links\"\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <h4>Connect With Me</h4>\n              <div className=\"social-grid\">\n                {socialLinks.map((social, index) => (\n                  <motion.a\n                    key={social.name}\n                    href={social.url}\n                    className=\"social-link\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    style={{ '--social-color': social.color }}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    {social.icon}\n                    <span>{social.name}</span>\n                  </motion.a>\n                ))}\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            className=\"contact-form-container\"\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <form className=\"contact-form\" onSubmit={handleSubmit}>\n              <h3>Send a Message</h3>\n              \n              <div className=\"form-group\">\n                <label htmlFor=\"name\">Full Name</label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  required\n                  placeholder=\"Your full name\"\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"email\">Email Address</label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  required\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"subject\">Subject</label>\n                <input\n                  type=\"text\"\n                  id=\"subject\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleInputChange}\n                  required\n                  placeholder=\"What is this regarding?\"\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"message\">Message</label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleInputChange}\n                  required\n                  rows=\"6\"\n                  placeholder=\"Your message here...\"\n                ></textarea>\n              </div>\n\n              <motion.button\n                type=\"submit\"\n                className=\"submit-btn\"\n                disabled={isSubmitting}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                {isSubmitting ? (\n                  <span className=\"loading\">Sending...</span>\n                ) : (\n                  <>\n                    <FaPaperPlane />\n                    Send Message\n                  </>\n                )}\n              </motion.button>\n\n              {submitStatus === 'success' && (\n                <motion.div\n                  className=\"success-message\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5 }}\n                >\n                  Thank you! Your message has been sent successfully.\n                </motion.div>\n              )}\n            </form>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,EAAEC,OAAO,EAAEC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,eAAe,EAAEC,OAAO,EAAEC,YAAY,QAAQ,gBAAgB;AACnI,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM0B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAET,IAAI;MAAEU;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCZ,WAAW,CAACa,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACZ,IAAI,GAAGU;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBT,eAAe,CAAC,IAAI,CAAC;;IAErB;IACAU,UAAU,CAAC,MAAM;MACfV,eAAe,CAAC,KAAK,CAAC;MACtBE,eAAe,CAAC,SAAS,CAAC;MAC1BR,WAAW,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;MAE9DY,UAAU,CAAC,MAAM;QACfR,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMS,WAAW,GAAG,CAClB;IACEC,IAAI,eAAExB,OAAA,CAACT,UAAU;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,KAAK,EAAE,OAAO;IACdZ,KAAK,EAAE,4BAA4B;IACnCa,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,eAAExB,OAAA,CAACR,OAAO;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,KAAK,EAAE,OAAO;IACdZ,KAAK,EAAE,mBAAmB;IAC1Ba,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,eAAExB,OAAA,CAACP,cAAc;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,QAAQ;IACfZ,KAAK,EAAE,qEAAqE;IAC5Ea,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAClB;IACEP,IAAI,eAAExB,OAAA,CAACN,UAAU;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBrB,IAAI,EAAE,UAAU;IAChByB,GAAG,EAAE,qCAAqC;IAC1CC,KAAK,EAAE;EACT,CAAC,EACD;IACET,IAAI,eAAExB,OAAA,CAACJ,eAAe;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBrB,IAAI,EAAE,gBAAgB;IACtByB,GAAG,EAAE,oDAAoD;IACzDC,KAAK,EAAE;EACT,CAAC,EACD;IACET,IAAI,eAAExB,OAAA,CAACH,OAAO;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBrB,IAAI,EAAE,OAAO;IACbyB,GAAG,EAAE,uCAAuC;IAC5CC,KAAK,EAAE;EACT,CAAC,EACD;IACET,IAAI,eAAExB,OAAA,CAACL,SAAS;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBrB,IAAI,EAAE,SAAS;IACfyB,GAAG,EAAE,iCAAiC;IACtCC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEC,GAAG,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAoB,CAAC,EAC5C;IAAED,GAAG,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAsB,CAAC,EACjD;IAAED,GAAG,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAoB,CAAC,CAC7C;EAED,oBACEpC,OAAA;IAASqC,EAAE,EAAC,SAAS;IAACC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC/CvC,OAAA;MAAKsC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBvC,OAAA,CAACV,MAAM,CAACkD,EAAE;QACRF,SAAS,EAAC,eAAe;QACzBG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BC,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QAAAT,QAAA,EAC1B;MAED;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAEZ5B,OAAA;QAAKsC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BvC,OAAA,CAACV,MAAM,CAAC2D,GAAG;UACTX,SAAS,EAAC,cAAc;UACxBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEQ,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCN,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEQ,CAAC,EAAE;UAAE,CAAE;UAClCL,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BC,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAT,QAAA,gBAEzBvC,OAAA;YAAAuC,QAAA,EAAI;UAAmB;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5B5B,OAAA;YAAGsC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAGnC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJ5B,OAAA;YAAKsC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BhB,WAAW,CAAC4B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BrD,OAAA,CAACV,MAAM,CAAC2D,GAAG;cAETX,SAAS,EAAC,cAAc;cACxBG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,WAAW,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAClCE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEQ,KAAK,EAAED,KAAK,GAAG;cAAI,CAAE;cAClDN,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cAAAT,QAAA,gBAEzBvC,OAAA;gBAAKsC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEa,IAAI,CAAC5B;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/C5B,OAAA;gBAAKsC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BvC,OAAA;kBAAAuC,QAAA,EAAKa,IAAI,CAACvB;gBAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EACpBwB,IAAI,CAACtB,IAAI,gBACR9B,OAAA;kBAAGuD,IAAI,EAAEH,IAAI,CAACtB,IAAK;kBAACQ,SAAS,EAAC,cAAc;kBAAAC,QAAA,EACzCa,IAAI,CAACnC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,gBAEJ5B,OAAA;kBAAAuC,QAAA,EAAIa,IAAI,CAACnC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACnB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GAjBDwB,IAAI,CAACvB,KAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBL,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5B,OAAA,CAACV,MAAM,CAAC2D,GAAG;YACTX,SAAS,EAAC,cAAc;YACxBG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEQ,KAAK,EAAE;YAAI,CAAE;YAC1CP,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAAT,QAAA,gBAEzBvC,OAAA;cAAAuC,QAAA,EAAI;YAAY;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB5B,OAAA;cAAKsC,SAAS,EAAC,YAAY;cAAAC,QAAA,EACxBL,WAAW,CAACiB,GAAG,CAAC,CAACK,IAAI,EAAEH,KAAK,kBAC3BrD,OAAA;gBAAoBsC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACvCvC,OAAA;kBAAMsC,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAEiB,IAAI,CAACrB;gBAAG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvC5B,OAAA;kBAAMsC,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEiB,IAAI,CAACpB;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFjC4B,IAAI,CAACrB,GAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGb5B,OAAA,CAACV,MAAM,CAAC2D,GAAG;YACTX,SAAS,EAAC,cAAc;YACxBG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEQ,KAAK,EAAE;YAAI,CAAE;YAC1CP,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAAT,QAAA,gBAEzBvC,OAAA;cAAAuC,QAAA,EAAI;YAAe;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB5B,OAAA;cAAKsC,SAAS,EAAC,aAAa;cAAAC,QAAA,EACzBR,WAAW,CAACoB,GAAG,CAAC,CAACM,MAAM,EAAEJ,KAAK,kBAC7BrD,OAAA,CAACV,MAAM,CAACoE,CAAC;gBAEPH,IAAI,EAAEE,MAAM,CAACzB,GAAI;gBACjBM,SAAS,EAAC,aAAa;gBACvBpB,MAAM,EAAC,QAAQ;gBACfyC,GAAG,EAAC,qBAAqB;gBACzBC,KAAK,EAAE;kBAAE,gBAAgB,EAAEH,MAAM,CAACxB;gBAAM,CAAE;gBAC1C4B,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC3BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAvB,QAAA,GAEzBkB,MAAM,CAACjC,IAAI,eACZxB,OAAA;kBAAAuC,QAAA,EAAOkB,MAAM,CAAClD;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAVrB6B,MAAM,CAAClD,IAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWR,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGb5B,OAAA,CAACV,MAAM,CAAC2D,GAAG;UACTX,SAAS,EAAC,wBAAwB;UAClCG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEQ,CAAC,EAAE;UAAG,CAAE;UAC/BN,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEQ,CAAC,EAAE;UAAE,CAAE;UAClCL,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BC,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAT,QAAA,eAEzBvC,OAAA;YAAMsC,SAAS,EAAC,cAAc;YAAC0B,QAAQ,EAAE5C,YAAa;YAAAmB,QAAA,gBACpDvC,OAAA;cAAAuC,QAAA,EAAI;YAAc;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEvB5B,OAAA;cAAKsC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvC,OAAA;gBAAOiE,OAAO,EAAC,MAAM;gBAAA1B,QAAA,EAAC;cAAS;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvC5B,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACX7B,EAAE,EAAC,MAAM;gBACT9B,IAAI,EAAC,MAAM;gBACXU,KAAK,EAAEZ,QAAQ,CAACE,IAAK;gBACrB4D,QAAQ,EAAEpD,iBAAkB;gBAC5BqD,QAAQ;gBACRC,WAAW,EAAC;cAAgB;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5B,OAAA;cAAKsC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvC,OAAA;gBAAOiE,OAAO,EAAC,OAAO;gBAAA1B,QAAA,EAAC;cAAa;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5C5B,OAAA;gBACEkE,IAAI,EAAC,OAAO;gBACZ7B,EAAE,EAAC,OAAO;gBACV9B,IAAI,EAAC,OAAO;gBACZU,KAAK,EAAEZ,QAAQ,CAACG,KAAM;gBACtB2D,QAAQ,EAAEpD,iBAAkB;gBAC5BqD,QAAQ;gBACRC,WAAW,EAAC;cAAwB;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5B,OAAA;cAAKsC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvC,OAAA;gBAAOiE,OAAO,EAAC,SAAS;gBAAA1B,QAAA,EAAC;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxC5B,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACX7B,EAAE,EAAC,SAAS;gBACZ9B,IAAI,EAAC,SAAS;gBACdU,KAAK,EAAEZ,QAAQ,CAACI,OAAQ;gBACxB0D,QAAQ,EAAEpD,iBAAkB;gBAC5BqD,QAAQ;gBACRC,WAAW,EAAC;cAAyB;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5B,OAAA;cAAKsC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvC,OAAA;gBAAOiE,OAAO,EAAC,SAAS;gBAAA1B,QAAA,EAAC;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxC5B,OAAA;gBACEqC,EAAE,EAAC,SAAS;gBACZ9B,IAAI,EAAC,SAAS;gBACdU,KAAK,EAAEZ,QAAQ,CAACK,OAAQ;gBACxByD,QAAQ,EAAEpD,iBAAkB;gBAC5BqD,QAAQ;gBACRE,IAAI,EAAC,GAAG;gBACRD,WAAW,EAAC;cAAsB;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEN5B,OAAA,CAACV,MAAM,CAACiF,MAAM;cACZL,IAAI,EAAC,QAAQ;cACb5B,SAAS,EAAC,YAAY;cACtBkC,QAAQ,EAAE7D,YAAa;cACvBkD,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAvB,QAAA,EAEzB5B,YAAY,gBACXX,OAAA;gBAAMsC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAU;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAE3C5B,OAAA,CAAAE,SAAA;gBAAAqC,QAAA,gBACEvC,OAAA,CAACF,YAAY;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAElB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY,CAAC,EAEff,YAAY,KAAK,SAAS,iBACzBb,OAAA,CAACV,MAAM,CAAC2D,GAAG;cACTX,SAAS,EAAC,iBAAiB;cAC3BG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/B8B,OAAO,EAAE;gBAAE/B,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAP,QAAA,EAC/B;YAED;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACxB,EAAA,CA7RID,OAAO;AAAAuE,EAAA,GAAPvE,OAAO;AA+Rb,eAAeA,OAAO;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}