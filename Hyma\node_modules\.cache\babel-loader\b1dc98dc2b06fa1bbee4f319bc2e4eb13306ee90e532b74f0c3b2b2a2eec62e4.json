{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaHeart, FaLinkedin, FaTwitter, FaGoogle, FaGraduationCap, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';\nimport '../styles/Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  const quickLinks = [{\n    name: 'About',\n    href: '/about'\n  }, {\n    name: 'Research',\n    href: '/research'\n  }, {\n    name: 'Publications',\n    href: '/publications'\n  }, {\n    name: 'Teaching',\n    href: '/teaching'\n  }, {\n    name: 'Contact',\n    href: '/contact'\n  }];\n  const socialLinks = [{\n    icon: /*#__PURE__*/_jsxDEV(FaLinkedin, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 13\n    }, this),\n    name: \"LinkedIn\",\n    url: \"https://linkedin.com/in/dr-academic\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaGoogle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this),\n    name: \"Google Scholar\",\n    url: \"https://scholar.google.com/citations?user=academic\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaGraduationCap, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this),\n    name: \"Academic Profile\",\n    url: \"https://orcid.org/0000-0000-0000-0000\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaTwitter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this),\n    name: \"Twitter\",\n    url: \"https://twitter.com/dr_academic\"\n  }];\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"footer-section\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            onClick: scrollToTop,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-logo\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"logo-circle\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Prof\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"logo-text\",\n                children: \"Ms. Hymavathi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"footer-description\",\n            children: \"Dedicated to advancing knowledge through innovative research, inspiring education, and meaningful collaboration in the field of computer science and artificial intelligence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-social\",\n            children: socialLinks.map((social, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n              href: social.url,\n              className: \"footer-social-link\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              \"aria-label\": social.name,\n              whileHover: {\n                scale: 1.1,\n                y: -2\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: social.icon\n            }, social.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"footer-section\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.1\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"footer-links\",\n            children: quickLinks.map((link, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: link.href,\n                className: \"footer-link\",\n                children: link.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this)\n            }, link.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"footer-section\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Contact Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-contact\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                className: \"contact-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"mailto:<EMAIL>\",\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n                className: \"contact-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"tel:+15551234567\",\n                children: \"+****************\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n                className: \"contact-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Computer Science Building\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 44\n                }, this), \"Room 301, University Campus\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"footer-section\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.3\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Research Areas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"research-areas\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Artificial Intelligence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Machine Learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Computer Vision\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Natural Language Processing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Data Science\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"footer-bottom\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        viewport: {\n          once: true\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"copyright\",\n            children: [\"\\xA9 \", currentYear, \" Dr. Academic Excellence. All rights reserved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"made-with\",\n            children: [\"Made with \", /*#__PURE__*/_jsxDEV(FaHeart, {\n              className: \"heart-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 25\n            }, this), \" for academic excellence\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "motion", "Link", "FaHeart", "FaLinkedin", "FaTwitter", "FaGoogle", "FaGraduationCap", "FaEnvelope", "FaPhone", "FaMapMarkerAlt", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "quickLinks", "name", "href", "socialLinks", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "url", "scrollToTop", "window", "scrollTo", "top", "behavior", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "viewport", "once", "to", "onClick", "map", "social", "index", "a", "target", "rel", "whileHover", "scale", "whileTap", "delay", "link", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaHeart, FaLinkedin, FaTwitter, FaGoogle, FaGraduationCap, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';\nimport '../styles/Footer.css';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const quickLinks = [\n    { name: 'About', href: '/about' },\n    { name: 'Research', href: '/research' },\n    { name: 'Publications', href: '/publications' },\n    { name: 'Teaching', href: '/teaching' },\n    { name: 'Contact', href: '/contact' }\n  ];\n\n  const socialLinks = [\n    {\n      icon: <FaLinkedin />,\n      name: \"LinkedIn\",\n      url: \"https://linkedin.com/in/dr-academic\"\n    },\n    {\n      icon: <FaGoogle />,\n      name: \"Google Scholar\",\n      url: \"https://scholar.google.com/citations?user=academic\"\n    },\n    {\n      icon: <FaGraduationCap />,\n      name: \"Academic Profile\",\n      url: \"https://orcid.org/0000-0000-0000-0000\"\n    },\n    {\n      icon: <FaTwitter />,\n      name: \"Twitter\",\n      url: \"https://twitter.com/dr_academic\"\n    }\n  ];\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  return (\n    <footer className=\"footer\">\n      <div className=\"container\">\n        <div className=\"footer-content\">\n          {/* Logo and Description */}\n          <motion.div\n            className=\"footer-section\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <Link to=\"/\" onClick={scrollToTop}>\n              <div className=\"footer-logo\">\n                <div className=\"logo-circle\">\n                  <span>Prof</span>\n                </div>\n                <span className=\"logo-text\">Ms. Hymavathi</span>\n              </div>\n            </Link>\n            <p className=\"footer-description\">\n              Dedicated to advancing knowledge through innovative research, \n              inspiring education, and meaningful collaboration in the field \n              of computer science and artificial intelligence.\n            </p>\n            <div className=\"footer-social\">\n              {socialLinks.map((social, index) => (\n                <motion.a\n                  key={social.name}\n                  href={social.url}\n                  className=\"footer-social-link\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  aria-label={social.name}\n                  whileHover={{ scale: 1.1, y: -2 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  {social.icon}\n                </motion.a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Quick Links */}\n          <motion.div\n            className=\"footer-section\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            viewport={{ once: true }}\n          >\n            <h4>Quick Links</h4>\n            <ul className=\"footer-links\">\n              {quickLinks.map((link, index) => (\n                <li key={link.name}>\n                  <Link\n                    to={link.href}\n                    className=\"footer-link\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Contact Info */}\n          <motion.div\n            className=\"footer-section\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <h4>Contact Info</h4>\n            <div className=\"footer-contact\">\n              <div className=\"contact-item\">\n                <FaEnvelope className=\"contact-icon\" />\n                <a href=\"mailto:<EMAIL>\">\n                  <EMAIL>\n                </a>\n              </div>\n              <div className=\"contact-item\">\n                <FaPhone className=\"contact-icon\" />\n                <a href=\"tel:+15551234567\">\n                  +****************\n                </a>\n              </div>\n              <div className=\"contact-item\">\n                <FaMapMarkerAlt className=\"contact-icon\" />\n                <span>\n                  Computer Science Building<br />\n                  Room 301, University Campus\n                </span>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Research Areas */}\n          <motion.div\n            className=\"footer-section\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n            viewport={{ once: true }}\n          >\n            <h4>Research Areas</h4>\n            <ul className=\"research-areas\">\n              <li>Artificial Intelligence</li>\n              <li>Machine Learning</li>\n              <li>Computer Vision</li>\n              <li>Natural Language Processing</li>\n              <li>Data Science</li>\n            </ul>\n          </motion.div>\n        </div>\n\n        {/* Footer Bottom */}\n        <motion.div\n          className=\"footer-bottom\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"footer-bottom-content\">\n            <p className=\"copyright\">\n              © {currentYear} Dr. Academic Excellence. All rights reserved.\n            </p>\n            <p className=\"made-with\">\n              Made with <FaHeart className=\"heart-icon\" /> for academic excellence\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,UAAU,EAAEC,OAAO,EAAEC,cAAc,QAAQ,gBAAgB;AAC/H,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,MAAMC,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAS,CAAC,EACjC;IAAED,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACvC;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAgB,CAAC,EAC/C;IAAED,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACvC;IAAED,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,CACtC;EAED,MAAMC,WAAW,GAAG,CAClB;IACEC,IAAI,eAAET,OAAA,CAACR,UAAU;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBP,IAAI,EAAE,UAAU;IAChBQ,GAAG,EAAE;EACP,CAAC,EACD;IACEL,IAAI,eAAET,OAAA,CAACN,QAAQ;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBP,IAAI,EAAE,gBAAgB;IACtBQ,GAAG,EAAE;EACP,CAAC,EACD;IACEL,IAAI,eAAET,OAAA,CAACL,eAAe;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBP,IAAI,EAAE,kBAAkB;IACxBQ,GAAG,EAAE;EACP,CAAC,EACD;IACEL,IAAI,eAAET,OAAA,CAACP,SAAS;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBP,IAAI,EAAE,SAAS;IACfQ,GAAG,EAAE;EACP,CAAC,CACF;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBC,MAAM,CAACC,QAAQ,CAAC;MACdC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,oBACEnB,OAAA;IAAQoB,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBrB,OAAA;MAAKoB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBrB,OAAA;QAAKoB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAE7BrB,OAAA,CAACX,MAAM,CAACiC,GAAG;UACTF,SAAS,EAAC,gBAAgB;UAC1BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BC,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAT,QAAA,gBAEzBrB,OAAA,CAACV,IAAI;YAACyC,EAAE,EAAC,GAAG;YAACC,OAAO,EAAEjB,WAAY;YAAAM,QAAA,eAChCrB,OAAA;cAAKoB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrB,OAAA;gBAAKoB,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BrB,OAAA;kBAAAqB,QAAA,EAAM;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACNb,OAAA;gBAAMoB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAa;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPb,OAAA;YAAGoB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAIlC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJb,OAAA;YAAKoB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3Bb,WAAW,CAACyB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC7BnC,OAAA,CAACX,MAAM,CAAC+C,CAAC;cAEP7B,IAAI,EAAE2B,MAAM,CAACpB,GAAI;cACjBM,SAAS,EAAC,oBAAoB;cAC9BiB,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzB,cAAYJ,MAAM,CAAC5B,IAAK;cACxBiC,UAAU,EAAE;gBAAEC,KAAK,EAAE,GAAG;gBAAEf,CAAC,EAAE,CAAC;cAAE,CAAE;cAClCgB,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAnB,QAAA,EAEzBa,MAAM,CAACzB;YAAI,GATPyB,MAAM,CAAC5B,IAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUR,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbb,OAAA,CAACX,MAAM,CAACiC,GAAG;UACTF,SAAS,EAAC,gBAAgB;UAC1BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEc,KAAK,EAAE;UAAI,CAAE;UAC1Cb,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAT,QAAA,gBAEzBrB,OAAA;YAAAqB,QAAA,EAAI;UAAW;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBb,OAAA;YAAIoB,SAAS,EAAC,cAAc;YAAAC,QAAA,EACzBhB,UAAU,CAAC4B,GAAG,CAAC,CAACU,IAAI,EAAER,KAAK,kBAC1BnC,OAAA;cAAAqB,QAAA,eACErB,OAAA,CAACV,IAAI;gBACHyC,EAAE,EAAEY,IAAI,CAACpC,IAAK;gBACda,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAEtBsB,IAAI,CAACrC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC,GANA8B,IAAI,CAACrC,IAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOd,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGbb,OAAA,CAACX,MAAM,CAACiC,GAAG;UACTF,SAAS,EAAC,gBAAgB;UAC1BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEc,KAAK,EAAE;UAAI,CAAE;UAC1Cb,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAT,QAAA,gBAEzBrB,OAAA;YAAAqB,QAAA,EAAI;UAAY;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBb,OAAA;YAAKoB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrB,OAAA;cAAKoB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrB,OAAA,CAACJ,UAAU;gBAACwB,SAAS,EAAC;cAAc;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCb,OAAA;gBAAGO,IAAI,EAAC,mCAAmC;gBAAAc,QAAA,EAAC;cAE5C;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNb,OAAA;cAAKoB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrB,OAAA,CAACH,OAAO;gBAACuB,SAAS,EAAC;cAAc;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpCb,OAAA;gBAAGO,IAAI,EAAC,kBAAkB;gBAAAc,QAAA,EAAC;cAE3B;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNb,OAAA;cAAKoB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrB,OAAA,CAACF,cAAc;gBAACsB,SAAS,EAAC;cAAc;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3Cb,OAAA;gBAAAqB,QAAA,GAAM,2BACqB,eAAArB,OAAA;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,+BAEjC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbb,OAAA,CAACX,MAAM,CAACiC,GAAG;UACTF,SAAS,EAAC,gBAAgB;UAC1BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEc,KAAK,EAAE;UAAI,CAAE;UAC1Cb,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAT,QAAA,gBAEzBrB,OAAA;YAAAqB,QAAA,EAAI;UAAc;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBb,OAAA;YAAIoB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC5BrB,OAAA;cAAAqB,QAAA,EAAI;YAAuB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCb,OAAA;cAAAqB,QAAA,EAAI;YAAgB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBb,OAAA;cAAAqB,QAAA,EAAI;YAAe;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBb,OAAA;cAAAqB,QAAA,EAAI;YAA2B;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpCb,OAAA;cAAAqB,QAAA,EAAI;YAAY;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNb,OAAA,CAACX,MAAM,CAACiC,GAAG;QACTF,SAAS,EAAC,eAAe;QACzBG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEc,KAAK,EAAE;QAAI,CAAE;QAC1Cb,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QAAAT,QAAA,eAEzBrB,OAAA;UAAKoB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCrB,OAAA;YAAGoB,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,OACrB,EAACnB,WAAW,EAAC,gDACjB;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJb,OAAA;YAAGoB,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,YACb,eAAArB,OAAA,CAACT,OAAO;cAAC6B,SAAS,EAAC;YAAY;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAC9C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC+B,EAAA,GAlLI3C,MAAM;AAoLZ,eAAeA,MAAM;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}