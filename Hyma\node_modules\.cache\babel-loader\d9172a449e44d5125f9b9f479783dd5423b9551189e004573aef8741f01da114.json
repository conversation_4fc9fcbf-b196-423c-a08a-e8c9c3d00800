{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\components\\\\ThemeToggle.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { FaSun, FaMoon } from 'react-icons/fa';\nimport { useTheme } from '../contexts/ThemeContext';\nimport '../styles/ThemeToggle.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeToggle = () => {\n  _s();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(motion.button, {\n    className: \"theme-toggle\",\n    onClick: toggleTheme,\n    whileHover: {\n      scale: 1.1\n    },\n    whileTap: {\n      scale: 0.95\n    },\n    initial: {\n      opacity: 0,\n      rotate: -180\n    },\n    animate: {\n      opacity: 1,\n      rotate: 0\n    },\n    transition: {\n      duration: 0.5\n    },\n    \"aria-label\": `Switch to ${isDarkMode ? 'light' : 'dark'} mode`,\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"toggle-icon-container\",\n      animate: {\n        rotate: isDarkMode ? 180 : 0\n      },\n      transition: {\n        duration: 0.3,\n        ease: \"easeInOut\"\n      },\n      children: isDarkMode ? /*#__PURE__*/_jsxDEV(FaSun, {\n        className: \"toggle-icon sun-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(FaMoon, {\n        className: \"toggle-icon moon-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"toggle-background\",\n      animate: {\n        background: isDarkMode ? 'linear-gradient(135deg, #1a237e 0%, #3949ab 100%)' : 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)'\n      },\n      transition: {\n        duration: 0.3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"toggle-tooltip\",\n      children: isDarkMode ? 'Light Mode' : 'Dark Mode'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeToggle, \"MY/fJVj7pNG84xK2IRXuobEs7Rg=\", false, function () {\n  return [useTheme];\n});\n_c = ThemeToggle;\nexport default ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");", "map": {"version": 3, "names": ["React", "motion", "FaSun", "FaMoon", "useTheme", "jsxDEV", "_jsxDEV", "ThemeToggle", "_s", "isDarkMode", "toggleTheme", "button", "className", "onClick", "whileHover", "scale", "whileTap", "initial", "opacity", "rotate", "animate", "transition", "duration", "children", "div", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/components/ThemeToggle.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { FaSun, FaMoon } from 'react-icons/fa';\nimport { useTheme } from '../contexts/ThemeContext';\nimport '../styles/ThemeToggle.css';\n\nconst ThemeToggle = () => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <motion.button\n      className=\"theme-toggle\"\n      onClick={toggleTheme}\n      whileHover={{ scale: 1.1 }}\n      whileTap={{ scale: 0.95 }}\n      initial={{ opacity: 0, rotate: -180 }}\n      animate={{ opacity: 1, rotate: 0 }}\n      transition={{ duration: 0.5 }}\n      aria-label={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}\n    >\n      <motion.div\n        className=\"toggle-icon-container\"\n        animate={{ rotate: isDarkMode ? 180 : 0 }}\n        transition={{ duration: 0.3, ease: \"easeInOut\" }}\n      >\n        {isDarkMode ? (\n          <FaSun className=\"toggle-icon sun-icon\" />\n        ) : (\n          <FaMoon className=\"toggle-icon moon-icon\" />\n        )}\n      </motion.div>\n      \n      <motion.div\n        className=\"toggle-background\"\n        animate={{\n          background: isDarkMode \n            ? 'linear-gradient(135deg, #1a237e 0%, #3949ab 100%)'\n            : 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)'\n        }}\n        transition={{ duration: 0.3 }}\n      />\n      \n      <span className=\"toggle-tooltip\">\n        {isDarkMode ? 'Light Mode' : 'Dark Mode'}\n      </span>\n    </motion.button>\n  );\n};\n\nexport default ThemeToggle;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,KAAK,EAAEC,MAAM,QAAQ,gBAAgB;AAC9C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAGN,QAAQ,CAAC,CAAC;EAE9C,oBACEE,OAAA,CAACL,MAAM,CAACU,MAAM;IACZC,SAAS,EAAC,cAAc;IACxBC,OAAO,EAAEH,WAAY;IACrBI,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE;IAC3BC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAC1BE,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;IAAI,CAAE;IACtCC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE;IACnCE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAC9B,cAAY,aAAab,UAAU,GAAG,OAAO,GAAG,MAAM,OAAQ;IAAAc,QAAA,gBAE9DjB,OAAA,CAACL,MAAM,CAACuB,GAAG;MACTZ,SAAS,EAAC,uBAAuB;MACjCQ,OAAO,EAAE;QAAED,MAAM,EAAEV,UAAU,GAAG,GAAG,GAAG;MAAE,CAAE;MAC1CY,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEG,IAAI,EAAE;MAAY,CAAE;MAAAF,QAAA,EAEhDd,UAAU,gBACTH,OAAA,CAACJ,KAAK;QAACU,SAAS,EAAC;MAAsB;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE1CvB,OAAA,CAACH,MAAM;QAACS,SAAS,EAAC;MAAuB;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC5C;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAEbvB,OAAA,CAACL,MAAM,CAACuB,GAAG;MACTZ,SAAS,EAAC,mBAAmB;MAC7BQ,OAAO,EAAE;QACPU,UAAU,EAAErB,UAAU,GAClB,mDAAmD,GACnD;MACN,CAAE;MACFY,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eAEFvB,OAAA;MAAMM,SAAS,EAAC,gBAAgB;MAAAW,QAAA,EAC7Bd,UAAU,GAAG,YAAY,GAAG;IAAW;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEpB,CAAC;AAACrB,EAAA,CAzCID,WAAW;EAAA,QACqBH,QAAQ;AAAA;AAAA2B,EAAA,GADxCxB,WAAW;AA2CjB,eAAeA,WAAW;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}