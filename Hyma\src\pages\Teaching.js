import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaGraduationCap, FaChalkboardTeacher, FaStar, FaUsers, FaBook, FaArrowLeft } from 'react-icons/fa';
import '../styles/Teaching.css';
import '../styles/PageLayout.css';

const Teaching = () => {
  const [activeTab, setActiveTab] = useState('courses');

  const courses = [
    {
      code: "CERT 01",
      title: "What is Data Science?",
      level: "Coursera",
      students: "Self-paced",
      rating: 5.0,
      description: "Comprehensive course on data science fundamentals and applications.",
      topics: ["Data Analysis", "Statistical Methods", "Data Visualization", "Python Programming"]
    },
    {
      code: "CERT 02",
      title: ".Net Full Stack Development",
      level: "Wipro",
      students: "Professional",
      rating: 5.0,
      description: "Complete full-stack development using .NET technologies.",
      topics: ["ASP.NET", "C#", "SQL Server", "Web Development", "API Development"]
    },
    {
      code: "CERT 03",
      title: "Project Management Specialization",
      level: "Coursera",
      students: "Professional",
      rating: 5.0,
      description: "Comprehensive project management training covering all phases.",
      topics: ["Project Planning", "Execution", "Risk Management", "Team Leadership"]
    },
    {
      code: "CERT 04",
      title: "Programming Fundamentals using Python",
      level: "Infosys",
      students: "Professional",
      rating: 5.0,
      description: "Foundation course in Python programming and software development.",
      topics: ["Python Basics", "Data Structures", "Algorithms", "Object-Oriented Programming"]
    }
  ];

  const achievements = [
    {
      icon: <FaGraduationCap />,
      title: "Women Entrepreneurship Development Program",
      year: "2023",
      description: "Three-week program by Department of Science and Technology, Govt. of India"
    },
    {
      icon: <FaStar />,
      title: "Cloud Infrastructure (AWS) FDP",
      year: "2023",
      description: "One-week National FDP by Brainovision Solutions in collaboration with AICTE"
    },
    {
      icon: <FaBook />,
      title: "AI and Cyber Security FDP",
      year: "2022",
      description: "One-week FDP on Recent Trends in AI and Cyber Security"
    }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      course: "CS 601 - Advanced Machine Learning",
      text: "Dr. Academic's teaching style is exceptional. Complex concepts become clear through practical examples and hands-on projects.",
      rating: 5
    },
    {
      name: "Michael Chen",
      course: "CS 401 - Introduction to AI",
      text: "The best professor I've had. Always available for questions and genuinely cares about student success.",
      rating: 5
    },
    {
      name: "Emily Rodriguez",
      course: "CS 501 - Computer Vision",
      text: "Challenging course but incredibly rewarding. The projects were directly applicable to real-world problems.",
      rating: 5
    }
  ];

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <FaStar
        key={i}
        className={`star ${i < rating ? 'filled' : ''}`}
      />
    ));
  };

  return (
    <div className="page-container">
      {/* Page Header */}
      <motion.div
        className="page-header"
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container">
          <Link to="/" className="back-link">
            <FaArrowLeft />
            Back to Home
          </Link>
          <h1 className="page-title">Teaching & Mentorship</h1>
          <p className="page-subtitle">Discover my courses, teaching philosophy, and achievements</p>
        </div>
      </motion.div>

      <section className="section teaching">
        <div className="container">
          {/* Teaching Philosophy */}
          <motion.div
            className="teaching-philosophy"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="philosophy-content">
              <FaChalkboardTeacher className="philosophy-icon" />
              <div className="philosophy-text">
                <h3>Teaching Philosophy</h3>
                <p>
                  I believe in creating an inclusive, engaging learning environment where students 
                  are encouraged to think critically and explore innovative solutions. My approach 
                  combines theoretical foundations with practical applications, ensuring students 
                  gain both deep understanding and real-world skills.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Tab Navigation */}
          <motion.div
            className="teaching-tabs"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <button
              className={`tab-btn ${activeTab === 'courses' ? 'active' : ''}`}
              onClick={() => setActiveTab('courses')}
            >
              <FaBook />
              Certifications
            </button>
            <button
              className={`tab-btn ${activeTab === 'achievements' ? 'active' : ''}`}
              onClick={() => setActiveTab('achievements')}
            >
              <FaStar />
              Workshops
            </button>
            <button
              className={`tab-btn ${activeTab === 'testimonials' ? 'active' : ''}`}
              onClick={() => setActiveTab('testimonials')}
            >
              <FaUsers />
              Testimonials
            </button>
          </motion.div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'courses' && (
              <motion.div
                className="courses-grid"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {courses.map((course, index) => (
                  <motion.div
                    key={course.code}
                    className="course-card"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="course-header">
                      <div className="course-code">{course.code}</div>
                      <div className="course-level">{course.level}</div>
                    </div>
                    
                    <h4 className="course-title">{course.title}</h4>
                    <p className="course-description">{course.description}</p>
                    
                    <div className="course-topics">
                      {course.topics.map(topic => (
                        <span key={topic} className="topic-tag">{topic}</span>
                      ))}
                    </div>
                    
                    <div className="course-stats">
                      <div className="stat">
                        <FaUsers />
                        <span>{course.students} students</span>
                      </div>
                      <div className="stat">
                        <div className="rating">
                          {renderStars(Math.floor(course.rating))}
                          <span className="rating-number">{course.rating}</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {activeTab === 'achievements' && (
              <motion.div
                className="achievements-grid"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {achievements.map((achievement, index) => (
                  <motion.div
                    key={achievement.title}
                    className="achievement-card"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="achievement-icon">{achievement.icon}</div>
                    <div className="achievement-content">
                      <h4>{achievement.title}</h4>
                      <div className="achievement-year">{achievement.year}</div>
                      <p>{achievement.description}</p>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {activeTab === 'testimonials' && (
              <motion.div
                className="testimonials-grid"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {testimonials.map((testimonial, index) => (
                  <motion.div
                    key={testimonial.name}
                    className="testimonial-card"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="testimonial-rating">
                      {renderStars(testimonial.rating)}
                    </div>
                    <p className="testimonial-text">"{testimonial.text}"</p>
                    <div className="testimonial-author">
                      <strong>{testimonial.name}</strong>
                      <span className="testimonial-course">{testimonial.course}</span>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Teaching;
