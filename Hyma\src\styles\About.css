.about {
  background: var(--background-light);
  padding: 6rem 0;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-bottom: 4rem;
}

.about-text {
  max-width: 600px;
}

.about-intro h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.about-intro p {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.expertise-section {
  margin-top: 2.5rem;
}

.expertise-section h4 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.expertise-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.expertise-item {
  background: var(--gradient-primary);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 25px;
  text-align: center;
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition-medium);
}

.expertise-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.about-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.about-image .image-placeholder {
  width: 350px;
  height: 350px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-heavy);
  position: relative;
  overflow: hidden;
}

.about-image .image-placeholder::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

.about-image .placeholder-content {
  text-align: center;
  z-index: 1;
}

.about-image .placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.about-image .placeholder-content p {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
}

.stats-section {
  margin-top: 4rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-card {
  background: white;
  padding: 2.5rem 1.5rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: var(--transition-medium);
}

.stat-card:hover {
  box-shadow: var(--shadow-medium);
}

.stat-icon {
  font-size: 2.5rem;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-family: 'Playfair Display', serif;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1rem;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .about-content {
    gap: 3rem;
  }
  
  .about-image .image-placeholder {
    width: 300px;
    height: 300px;
  }
}

@media (max-width: 768px) {
  .about {
    padding: 4rem 0;
  }
  
  .about-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
  
  .about-text {
    max-width: 100%;
  }
  
  .expertise-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
  }
  
  .expertise-item {
    font-size: 0.8rem;
    padding: 0.6rem 0.8rem;
  }
  
  .about-image .image-placeholder {
    width: 280px;
    height: 280px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
  }
  
  .stat-card {
    padding: 2rem 1rem;
  }
  
  .stat-icon {
    font-size: 2rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .about-intro h3 {
    font-size: 1.5rem;
  }
  
  .expertise-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .about-image .image-placeholder {
    width: 250px;
    height: 250px;
  }
  
  .about-image .placeholder-icon {
    font-size: 3rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Profile Image Styles */
.profile-image-about {
  width: 350px;
  height: 350px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
  margin: 0 auto;
  background: white;
  padding: 8px;
}

.about-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px;
  transition: var(--transition-medium);
}

.about-photo:hover {
  transform: scale(1.02);
}
