# Professor <PERSON> - React.js

A modern, professional React.js portfolio website designed specifically for academic professionals, professors, and researchers.

## 🌟 Features

- **Modern React.js Architecture** - Built with functional components and hooks
- **Responsive Design** - Perfect on all devices and screen sizes
- **Professional Academic Focus** - Tailored for professors and researchers
- **Smooth Animations** - Powered by <PERSON>amer Motion
- **Interactive Components** - Engaging user experience
- **Contact Form** - Functional contact form with validation
- **SEO Optimized** - Ready for search engines

## 📄 Sections

- **Hero Section** - Professional introduction with call-to-action
- **About** - Personal background, expertise, and achievements
- **Research** - Research areas, current projects, and impact
- **Publications** - Searchable and filterable publication list
- **Teaching** - Courses, achievements, and student testimonials
- **Contact** - Contact information, office hours, and contact form

## 🚀 Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn package manager

### Installation

1. **Clone or navigate to the project directory:**
   ```bash
   cd Hyma
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm start
   ```

4. **Open your browser and visit:**
   ```
   http://localhost:3000
   ```

## 🛠️ Built With

- **React.js** - Frontend framework
- **Framer Motion** - Animation library
- **React Icons** - Icon components
- **CSS3** - Modern styling with custom properties
- **EmailJS** - Contact form functionality

## 📁 Project Structure

```
Hyma/
├── public/
│   ├── index.html
│   └── manifest.json
├── src/
│   ├── components/
│   │   ├── Header.js
│   │   ├── Hero.js
│   │   ├── About.js
│   │   ├── Research.js
│   │   ├── Publications.js
│   │   ├── Teaching.js
│   │   ├── Contact.js
│   │   ├── Footer.js
│   │   └── LoadingScreen.js
│   ├── styles/
│   │   ├── index.css
│   │   ├── App.css
│   │   ├── Header.css
│   │   ├── Hero.css
│   │   ├── About.css
│   │   ├── Research.css
│   │   ├── Publications.css
│   │   ├── Teaching.css
│   │   ├── Contact.css
│   │   ├── Footer.css
│   │   └── LoadingScreen.css
│   ├── App.js
│   └── index.js
├── package.json
└── README.md
```

## 🎨 Customization

### Personal Information

1. **Update personal details in components:**
   - `Hero.js` - Name, title, and description
   - `About.js` - Biography and expertise
   - `Contact.js` - Contact information and office hours

2. **Add your research data:**
   - `Research.js` - Research areas and projects
   - `Publications.js` - Publication list
   - `Teaching.js` - Courses and achievements

### Styling

1. **Colors and themes:**
   - Edit CSS custom properties in `src/styles/index.css`
   - Modify gradient colors and color scheme

2. **Fonts:**
   - Update Google Fonts imports in `public/index.html`
   - Change font families in CSS files

### Images

1. **Add your photos:**
   - Replace placeholder content in Hero and About sections
   - Add actual images to `public/images/` directory

## 📱 Responsive Design

The portfolio is fully responsive and optimized for:
- Desktop computers (1200px+)
- Tablets (768px - 1199px)
- Mobile phones (320px - 767px)

## 🔧 Available Scripts

- `npm start` - Runs the app in development mode
- `npm build` - Builds the app for production
- `npm test` - Launches the test runner
- `npm eject` - Ejects from Create React App (one-way operation)

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

### Deploy to Vercel

1. Install Vercel CLI:
   ```bash
   npm i -g vercel
   ```

2. Deploy:
   ```bash
   vercel
   ```

### Deploy to Netlify

1. Build the project:
   ```bash
   npm run build
   ```

2. Drag and drop the `build` folder to Netlify

## 📧 Contact Form Setup

To enable the contact form functionality:

1. **Sign up for EmailJS:**
   - Visit [EmailJS](https://www.emailjs.com/)
   - Create an account and set up email service

2. **Configure EmailJS:**
   - Update the EmailJS configuration in `Contact.js`
   - Add your service ID, template ID, and user ID

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- React.js community for excellent documentation
- Framer Motion for smooth animations
- Google Fonts for typography
- React Icons for beautiful icons

## 📞 Support

If you have any questions or need help customizing the portfolio, feel free to reach out!

---

**Made with ❤️ for academic excellence**
