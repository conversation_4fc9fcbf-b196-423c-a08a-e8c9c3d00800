{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\uspro\\\\Hyma\\\\src\\\\pages\\\\Publications.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaBook, FaExternalLinkAlt, FaQuoteLeft, FaFilter, FaSearch, FaArrowLeft } from 'react-icons/fa';\nimport '../styles/Publications.css';\nimport '../styles/PageLayout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Publications = () => {\n  _s();\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const categories = [{\n    id: 'all',\n    label: 'All Publications'\n  }, {\n    id: 'journal',\n    label: 'Journal Articles'\n  }, {\n    id: 'conference',\n    label: 'Conference Papers'\n  }, {\n    id: 'patent',\n    label: 'Patents'\n  }];\n  const publications = [{\n    id: 1,\n    title: \"Prediction of the Most Influenced Gene in the Development of Breast Cancer Using the DELSTM Model\",\n    authors: \"Hymavathi Thottathyl, Kanadam Karteeka Pavan\",\n    journal: \"Ingénierie des Systèmes d'Information\",\n    year: 2025,\n    category: \"journal\",\n    citations: 0,\n    impact: \"High Impact\",\n    abstract: \"This paper presents a novel DELSTM model for predicting the most influenced genes in breast cancer development, contributing to early detection and treatment strategies.\",\n    doi: \"Vol. 30, No. 1, pp. 279-286\"\n  }, {\n    id: 2,\n    title: \"Enhancing Cyber Attack Detection in Network Traffic Using Adaptive Regression Techniques\",\n    authors: \"Dr. Talluri.Sunil Kumar, P. Jyothi, Dr. Rajesh Kumar Verma, Padmini Debbarma, Dr.N.V.S. Pavan Kumar, I. Naga Padmaja, Hymavathi Thottathyl, Dr.M. Sathish Kumar\",\n    journal: \"Journal of Theoretical and Applied Information Technology\",\n    year: 2024,\n    category: \"journal\",\n    citations: 0,\n    impact: \"High Impact\",\n    abstract: \"This research presents adaptive regression techniques for enhancing cyber attack detection in network traffic, improving security measures in modern networks.\",\n    doi: \"Vol. 102, No. 23, pp. 8655-8664\"\n  }, {\n    id: 3,\n    title: \"Differential Evolution Model for Identification of Most Influenced Gene in Brest Cancer Data\",\n    authors: \"Hymavathi Thottathyl, Kanadam Karteeka Pavan\",\n    journal: \"Ingénierie des Systèmes d'Information\",\n    year: 2022,\n    category: \"journal\",\n    citations: 0,\n    impact: \"High Impact\",\n    abstract: \"This paper presents a differential evolution model for identifying the most influenced genes in breast cancer data, providing insights for targeted therapy approaches.\",\n    doi: \"Vol. 27, No. 3, pp. 487-493\"\n  }, {\n    id: 4,\n    title: \"Microarray Breast Cancer Data Clustering Using Map Reduce Based K-Means Algorithm\",\n    authors: \"Hymavathi Thottathyl, Kanadam Karteeka Pavan, Rajeev Priyatam Panchadula\",\n    journal: \"Revue d'Intelligence Artificielle\",\n    year: 2020,\n    category: \"journal\",\n    citations: 0,\n    impact: \"High Impact\",\n    abstract: \"This research presents a Map Reduce based K-Means algorithm for clustering microarray breast cancer data, enabling efficient analysis of large-scale genomic datasets.\",\n    doi: \"Vol. 34, No. 6, pp. 763-769\"\n  }, {\n    id: 5,\n    title: \"Artificial Intelligence (AI) Driven Personal Assistant with Multi-Platform Integration\",\n    authors: \"Ms. Hymavathi.T et. al.\",\n    journal: \"Indian Patent\",\n    year: 2023,\n    category: \"patent\",\n    citations: 0,\n    impact: \"High Impact\",\n    abstract: \"Patent for an AI-driven personal assistant system with multi-platform integration capabilities, filed and published in 2023.\",\n    doi: \"Application No. ************ A\"\n  }];\n  const filteredPublications = publications.filter(pub => {\n    const matchesCategory = selectedCategory === 'all' || pub.category === selectedCategory;\n    const matchesSearch = pub.title.toLowerCase().includes(searchTerm.toLowerCase()) || pub.authors.toLowerCase().includes(searchTerm.toLowerCase()) || pub.journal.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesCategory && matchesSearch;\n  });\n  const totalCitations = publications.reduce((sum, pub) => sum + pub.citations, 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"page-header\",\n      initial: {\n        opacity: 0,\n        y: -30\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"back-link\",\n          children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: \"Publications & Research Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Browse my academic publications and research papers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section publications\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"publication-stats\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: publications.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Publications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Patent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: \"SCOPUS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Indexed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: \"2020-2025\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Active Years\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"publication-controls\",\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.4\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-box\",\n            children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search publications...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"category-filters\",\n            children: [/*#__PURE__*/_jsxDEV(FaFilter, {\n              className: \"filter-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `filter-btn ${selectedCategory === category.id ? 'active' : ''}`,\n              onClick: () => setSelectedCategory(category.id),\n              children: category.label\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"publications-list\",\n          children: filteredPublications.map((publication, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"publication-card\",\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.6 + index * 0.1\n            },\n            whileHover: {\n              y: -5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"publication-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"publication-icon\",\n                children: /*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"publication-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `impact-badge ${publication.impact.toLowerCase().replace(' ', '-')}`,\n                  children: publication.impact\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"publication-year\",\n                  children: publication.year\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"publication-title\",\n              children: publication.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"publication-authors\",\n              children: publication.authors\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"publication-journal\",\n              children: publication.journal\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"publication-abstract\",\n              children: [/*#__PURE__*/_jsxDEV(FaQuoteLeft, {\n                className: \"quote-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: publication.abstract\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"publication-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"publication-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"citations\",\n                  children: [publication.citations, \" citations\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"doi\",\n                  children: [\"DOI: \", publication.doi]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-outline publication-link\",\n                children: [/*#__PURE__*/_jsxDEV(FaExternalLinkAlt, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this), \"View Paper\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, publication.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), filteredPublications.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"no-results\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No publications found matching your criteria.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(Publications, \"oSzvDQd7hFA8YV3r3I9HwCAFkfg=\");\n_c = Publications;\nexport default Publications;\nvar _c;\n$RefreshReg$(_c, \"Publications\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "FaBook", "FaExternalLinkAlt", "FaQuoteLeft", "FaFilter", "FaSearch", "FaArrowLeft", "jsxDEV", "_jsxDEV", "Publications", "_s", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "searchTerm", "setSearchTerm", "categories", "id", "label", "publications", "title", "authors", "journal", "year", "category", "citations", "impact", "abstract", "doi", "filteredPublications", "filter", "pub", "matchesCategory", "matchesSearch", "toLowerCase", "includes", "totalCitations", "reduce", "sum", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "length", "type", "placeholder", "value", "onChange", "e", "target", "map", "onClick", "publication", "index", "whileHover", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/uspro/Hyma/src/pages/Publications.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { FaBook, FaExternalLinkAlt, FaQuoteLeft, FaFilter, FaSearch, FaArrowLeft } from 'react-icons/fa';\nimport '../styles/Publications.css';\nimport '../styles/PageLayout.css';\n\nconst Publications = () => {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  const categories = [\n    { id: 'all', label: 'All Publications' },\n    { id: 'journal', label: 'Journal Articles' },\n    { id: 'conference', label: 'Conference Papers' },\n    { id: 'patent', label: 'Patents' }\n  ];\n\n  const publications = [\n    {\n      id: 1,\n      title: \"Prediction of the Most Influenced Gene in the Development of Breast Cancer Using the DELSTM Model\",\n      authors: \"<PERSON><PERSON><PERSON><PERSON>hyl, Kanadam Karteeka Pavan\",\n      journal: \"Ingénierie des Systèmes d'Information\",\n      year: 2025,\n      category: \"journal\",\n      citations: 0,\n      impact: \"High Impact\",\n      abstract: \"This paper presents a novel DELSTM model for predicting the most influenced genes in breast cancer development, contributing to early detection and treatment strategies.\",\n      doi: \"Vol. 30, No. 1, pp. 279-286\"\n    },\n    {\n      id: 2,\n      title: \"Enhancing Cyber Attack Detection in Network Traffic Using Adaptive Regression Techniques\",\n      authors: \"Dr. Talluri.Sunil Kumar, P. Jyothi, Dr. Rajesh Kumar Verma, Padmini Debbarma, Dr.N.V.S. Pavan Kumar, I. Naga Padmaja, Hymavathi Thottathyl, Dr.M. Sathish Kumar\",\n      journal: \"Journal of Theoretical and Applied Information Technology\",\n      year: 2024,\n      category: \"journal\",\n      citations: 0,\n      impact: \"High Impact\",\n      abstract: \"This research presents adaptive regression techniques for enhancing cyber attack detection in network traffic, improving security measures in modern networks.\",\n      doi: \"Vol. 102, No. 23, pp. 8655-8664\"\n    },\n    {\n      id: 3,\n      title: \"Differential Evolution Model for Identification of Most Influenced Gene in Brest Cancer Data\",\n      authors: \"Hymavathi Thottathyl, Kanadam Karteeka Pavan\",\n      journal: \"Ingénierie des Systèmes d'Information\",\n      year: 2022,\n      category: \"journal\",\n      citations: 0,\n      impact: \"High Impact\",\n      abstract: \"This paper presents a differential evolution model for identifying the most influenced genes in breast cancer data, providing insights for targeted therapy approaches.\",\n      doi: \"Vol. 27, No. 3, pp. 487-493\"\n    },\n    {\n      id: 4,\n      title: \"Microarray Breast Cancer Data Clustering Using Map Reduce Based K-Means Algorithm\",\n      authors: \"Hymavathi Thottathyl, Kanadam Karteeka Pavan, Rajeev Priyatam Panchadula\",\n      journal: \"Revue d'Intelligence Artificielle\",\n      year: 2020,\n      category: \"journal\",\n      citations: 0,\n      impact: \"High Impact\",\n      abstract: \"This research presents a Map Reduce based K-Means algorithm for clustering microarray breast cancer data, enabling efficient analysis of large-scale genomic datasets.\",\n      doi: \"Vol. 34, No. 6, pp. 763-769\"\n    },\n    {\n      id: 5,\n      title: \"Artificial Intelligence (AI) Driven Personal Assistant with Multi-Platform Integration\",\n      authors: \"Ms. Hymavathi.T et. al.\",\n      journal: \"Indian Patent\",\n      year: 2023,\n      category: \"patent\",\n      citations: 0,\n      impact: \"High Impact\",\n      abstract: \"Patent for an AI-driven personal assistant system with multi-platform integration capabilities, filed and published in 2023.\",\n      doi: \"Application No. ************ A\"\n    }\n  ];\n\n  const filteredPublications = publications.filter(pub => {\n    const matchesCategory = selectedCategory === 'all' || pub.category === selectedCategory;\n    const matchesSearch = pub.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         pub.authors.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         pub.journal.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesCategory && matchesSearch;\n  });\n\n  const totalCitations = publications.reduce((sum, pub) => sum + pub.citations, 0);\n\n  return (\n    <div className=\"page-container\">\n      {/* Page Header */}\n      <motion.div\n        className=\"page-header\"\n        initial={{ opacity: 0, y: -30 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <div className=\"container\">\n          <Link to=\"/\" className=\"back-link\">\n            <FaArrowLeft />\n            Back to Home\n          </Link>\n          <h1 className=\"page-title\">Publications & Research Output</h1>\n          <p className=\"page-subtitle\">Browse my academic publications and research papers</p>\n        </div>\n      </motion.div>\n\n      <section className=\"section publications\">\n        <div className=\"container\">\n          {/* Publication Stats */}\n          <motion.div\n            className=\"publication-stats\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            <div className=\"stat-item\">\n              <div className=\"stat-number\">{publications.length}</div>\n              <div className=\"stat-label\">Publications</div>\n            </div>\n            <div className=\"stat-item\">\n              <div className=\"stat-number\">1</div>\n              <div className=\"stat-label\">Patent</div>\n            </div>\n            <div className=\"stat-item\">\n              <div className=\"stat-number\">SCOPUS</div>\n              <div className=\"stat-label\">Indexed</div>\n            </div>\n            <div className=\"stat-item\">\n              <div className=\"stat-number\">2020-2025</div>\n              <div className=\"stat-label\">Active Years</div>\n            </div>\n          </motion.div>\n\n          {/* Filters and Search */}\n          <motion.div\n            className=\"publication-controls\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            <div className=\"search-box\">\n              <FaSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search publications...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"search-input\"\n              />\n            </div>\n            \n            <div className=\"category-filters\">\n              <FaFilter className=\"filter-icon\" />\n              {categories.map(category => (\n                <button\n                  key={category.id}\n                  className={`filter-btn ${selectedCategory === category.id ? 'active' : ''}`}\n                  onClick={() => setSelectedCategory(category.id)}\n                >\n                  {category.label}\n                </button>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Publications List */}\n          <div className=\"publications-list\">\n            {filteredPublications.map((publication, index) => (\n              <motion.div\n                key={publication.id}\n                className=\"publication-card\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}\n                whileHover={{ y: -5 }}\n              >\n                <div className=\"publication-header\">\n                  <div className=\"publication-icon\">\n                    <FaBook />\n                  </div>\n                  <div className=\"publication-meta\">\n                    <span className={`impact-badge ${publication.impact.toLowerCase().replace(' ', '-')}`}>\n                      {publication.impact}\n                    </span>\n                    <span className=\"publication-year\">{publication.year}</span>\n                  </div>\n                </div>\n\n                <h3 className=\"publication-title\">{publication.title}</h3>\n                <p className=\"publication-authors\">{publication.authors}</p>\n                <p className=\"publication-journal\">{publication.journal}</p>\n\n                <div className=\"publication-abstract\">\n                  <FaQuoteLeft className=\"quote-icon\" />\n                  <p>{publication.abstract}</p>\n                </div>\n\n                <div className=\"publication-footer\">\n                  <div className=\"publication-stats\">\n                    <span className=\"citations\">{publication.citations} citations</span>\n                    <span className=\"doi\">DOI: {publication.doi}</span>\n                  </div>\n                  <button className=\"btn btn-outline publication-link\">\n                    <FaExternalLinkAlt />\n                    View Paper\n                  </button>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {filteredPublications.length === 0 && (\n            <motion.div\n              className=\"no-results\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              <p>No publications found matching your criteria.</p>\n            </motion.div>\n          )}\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Publications;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,gBAAgB;AACxG,OAAO,4BAA4B;AACnC,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMiB,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAmB,CAAC,EACxC;IAAED,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAmB,CAAC,EAC5C;IAAED,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAoB,CAAC,EAChD;IAAED,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,CACnC;EAED,MAAMC,YAAY,GAAG,CACnB;IACEF,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,mGAAmG;IAC1GC,OAAO,EAAE,8CAA8C;IACvDC,OAAO,EAAE,uCAAuC;IAChDC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,2KAA2K;IACrLC,GAAG,EAAE;EACP,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,0FAA0F;IACjGC,OAAO,EAAE,iKAAiK;IAC1KC,OAAO,EAAE,2DAA2D;IACpEC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,gKAAgK;IAC1KC,GAAG,EAAE;EACP,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,8FAA8F;IACrGC,OAAO,EAAE,8CAA8C;IACvDC,OAAO,EAAE,uCAAuC;IAChDC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,yKAAyK;IACnLC,GAAG,EAAE;EACP,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,mFAAmF;IAC1FC,OAAO,EAAE,0EAA0E;IACnFC,OAAO,EAAE,mCAAmC;IAC5CC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,wKAAwK;IAClLC,GAAG,EAAE;EACP,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,wFAAwF;IAC/FC,OAAO,EAAE,yBAAyB;IAClCC,OAAO,EAAE,eAAe;IACxBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,8HAA8H;IACxIC,GAAG,EAAE;EACP,CAAC,CACF;EAED,MAAMC,oBAAoB,GAAGV,YAAY,CAACW,MAAM,CAACC,GAAG,IAAI;IACtD,MAAMC,eAAe,GAAGpB,gBAAgB,KAAK,KAAK,IAAImB,GAAG,CAACP,QAAQ,KAAKZ,gBAAgB;IACvF,MAAMqB,aAAa,GAAGF,GAAG,CAACX,KAAK,CAACc,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CAAC,IAC3DH,GAAG,CAACV,OAAO,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CAAC,IAC5DH,GAAG,CAACT,OAAO,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CAAC;IACjF,OAAOF,eAAe,IAAIC,aAAa;EACzC,CAAC,CAAC;EAEF,MAAMG,cAAc,GAAGjB,YAAY,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEP,GAAG,KAAKO,GAAG,GAAGP,GAAG,CAACN,SAAS,EAAE,CAAC,CAAC;EAEhF,oBACEhB,OAAA;IAAK8B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7B/B,OAAA,CAACT,MAAM,CAACyC,GAAG;MACTF,SAAS,EAAC,aAAa;MACvBG,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAP,QAAA,eAE9B/B,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/B,OAAA,CAACR,IAAI;UAAC+C,EAAE,EAAC,GAAG;UAACT,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAChC/B,OAAA,CAACF,WAAW;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP3C,OAAA;UAAI8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAA8B;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9D3C,OAAA;UAAG8B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmD;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEb3C,OAAA;MAAS8B,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACvC/B,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAExB/B,OAAA,CAACT,MAAM,CAACyC,GAAG;UACTF,SAAS,EAAC,mBAAmB;UAC7BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEM,KAAK,EAAE;UAAI,CAAE;UAAAb,QAAA,gBAE1C/B,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/B,OAAA;cAAK8B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAErB,YAAY,CAACmC;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxD3C,OAAA;cAAK8B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACN3C,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/B,OAAA;cAAK8B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpC3C,OAAA;cAAK8B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACN3C,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/B,OAAA;cAAK8B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzC3C,OAAA;cAAK8B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACN3C,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/B,OAAA;cAAK8B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5C3C,OAAA;cAAK8B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb3C,OAAA,CAACT,MAAM,CAACyC,GAAG;UACTF,SAAS,EAAC,sBAAsB;UAChCG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEM,KAAK,EAAE;UAAI,CAAE;UAAAb,QAAA,gBAE1C/B,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/B,OAAA,CAACH,QAAQ;cAACiC,SAAS,EAAC;YAAa;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpC3C,OAAA;cACE8C,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,wBAAwB;cACpCC,KAAK,EAAE3C,UAAW;cAClB4C,QAAQ,EAAGC,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/ClB,SAAS,EAAC;YAAc;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3C,OAAA;YAAK8B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B/B,OAAA,CAACJ,QAAQ;cAACkC,SAAS,EAAC;YAAa;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnCpC,UAAU,CAAC6C,GAAG,CAACrC,QAAQ,iBACtBf,OAAA;cAEE8B,SAAS,EAAE,cAAc3B,gBAAgB,KAAKY,QAAQ,CAACP,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC5E6C,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAACW,QAAQ,CAACP,EAAE,CAAE;cAAAuB,QAAA,EAE/ChB,QAAQ,CAACN;YAAK,GAJVM,QAAQ,CAACP,EAAE;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKV,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb3C,OAAA;UAAK8B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC/BX,oBAAoB,CAACgC,GAAG,CAAC,CAACE,WAAW,EAAEC,KAAK,kBAC3CvD,OAAA,CAACT,MAAM,CAACyC,GAAG;YAETF,SAAS,EAAC,kBAAkB;YAC5BG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEM,KAAK,EAAE,GAAG,GAAGW,KAAK,GAAG;YAAI,CAAE;YACxDC,UAAU,EAAE;cAAErB,CAAC,EAAE,CAAC;YAAE,CAAE;YAAAJ,QAAA,gBAEtB/B,OAAA;cAAK8B,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjC/B,OAAA;gBAAK8B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/B/B,OAAA,CAACP,MAAM;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACN3C,OAAA;gBAAK8B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B/B,OAAA;kBAAM8B,SAAS,EAAE,gBAAgBwB,WAAW,CAACrC,MAAM,CAACQ,WAAW,CAAC,CAAC,CAACgC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAG;kBAAA1B,QAAA,EACnFuB,WAAW,CAACrC;gBAAM;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACP3C,OAAA;kBAAM8B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEuB,WAAW,CAACxC;gBAAI;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3C,OAAA;cAAI8B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAEuB,WAAW,CAAC3C;YAAK;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1D3C,OAAA;cAAG8B,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEuB,WAAW,CAAC1C;YAAO;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5D3C,OAAA;cAAG8B,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEuB,WAAW,CAACzC;YAAO;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE5D3C,OAAA;cAAK8B,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC/B,OAAA,CAACL,WAAW;gBAACmC,SAAS,EAAC;cAAY;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtC3C,OAAA;gBAAA+B,QAAA,EAAIuB,WAAW,CAACpC;cAAQ;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eAEN3C,OAAA;cAAK8B,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjC/B,OAAA;gBAAK8B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/B,OAAA;kBAAM8B,SAAS,EAAC,WAAW;kBAAAC,QAAA,GAAEuB,WAAW,CAACtC,SAAS,EAAC,YAAU;gBAAA;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpE3C,OAAA;kBAAM8B,SAAS,EAAC,KAAK;kBAAAC,QAAA,GAAC,OAAK,EAACuB,WAAW,CAACnC,GAAG;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACN3C,OAAA;gBAAQ8B,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAClD/B,OAAA,CAACN,iBAAiB;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,cAEvB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GArCDW,WAAW,CAAC9C,EAAE;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsCT,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELvB,oBAAoB,CAACyB,MAAM,KAAK,CAAC,iBAChC7C,OAAA,CAACT,MAAM,CAACyC,GAAG;UACTF,SAAS,EAAC,YAAY;UACtBG,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBG,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,eAE9B/B,OAAA;YAAA+B,QAAA,EAAG;UAA6C;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACzC,EAAA,CA9NID,YAAY;AAAAyD,EAAA,GAAZzD,YAAY;AAgOlB,eAAeA,YAAY;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}